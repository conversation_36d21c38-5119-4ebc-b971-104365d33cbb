import { Router } from 'express';
import logger from '../utils/logger.js';
import { AnalyticsService } from '../services/AnalyticsService.js';

export default function createAnalyticsRoutes(analyticsService: AnalyticsService) {
  const router = Router();

  // Get overall performance metrics
  router.get('/performance', async (req, res) => {
    try {
      const metrics = analyticsService.getOverallMetrics();
      
      res.json({
        success: true,
        data: metrics
      });
    } catch (error) {
      logger.error('Error getting performance metrics:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve performance metrics'
      });
    }
  });

  // Get strategy-specific metrics
  router.get('/performance/strategies', async (req, res) => {
    try {
      const strategyMetrics = analyticsService.getStrategyMetrics();
      
      res.json({
        success: true,
        data: strategyMetrics
      });
    } catch (error) {
      logger.error('Error getting strategy metrics:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve strategy metrics'
      });
    }
  });

  // Get time series data
  router.get('/timeseries', async (req, res) => {
    try {
      const { hours = '24' } = req.query;
      const hoursNum = parseInt(hours as string);
      
      if (isNaN(hoursNum) || hoursNum <= 0) {
        return res.status(400).json({
          success: false,
          error: 'Invalid hours parameter'
        });
      }

      const timeSeriesData = analyticsService.getTimeSeriesData(hoursNum);
      
      res.json({
        success: true,
        data: timeSeriesData,
        count: timeSeriesData.length,
        timeframe: `${hoursNum} hours`
      });
    } catch (error) {
      logger.error('Error getting time series data:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve time series data'
      });
    }
  });

  // Get daily metrics
  router.get('/daily', async (req, res) => {
    try {
      const { days = '7' } = req.query;
      const daysNum = parseInt(days as string);
      
      if (isNaN(daysNum) || daysNum <= 0 || daysNum > 30) {
        return res.status(400).json({
          success: false,
          error: 'Days parameter must be between 1 and 30'
        });
      }

      const dailyMetrics = analyticsService.getDailyMetrics(daysNum);
      
      res.json({
        success: true,
        data: dailyMetrics,
        count: dailyMetrics.length,
        timeframe: `${daysNum} days`
      });
    } catch (error) {
      logger.error('Error getting daily metrics:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve daily metrics'
      });
    }
  });

  // Get asset performance
  router.get('/assets', async (req, res) => {
    try {
      const { limit = '20' } = req.query;
      const limitNum = Math.min(parseInt(limit as string) || 20, 100);
      
      let assetPerformance = analyticsService.getAssetPerformance();
      assetPerformance = assetPerformance.slice(0, limitNum);
      
      res.json({
        success: true,
        data: assetPerformance,
        count: assetPerformance.length,
        limit: limitNum
      });
    } catch (error) {
      logger.error('Error getting asset performance:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve asset performance'
      });
    }
  });

  // Get top performing assets
  router.get('/assets/top', async (req, res) => {
    try {
      const { limit = '10', sortBy = 'totalProfit' } = req.query;
      const limitNum = Math.min(parseInt(limit as string) || 10, 50);
      
      let assetPerformance = analyticsService.getAssetPerformance();
      
      // Sort by specified metric
      if (sortBy === 'winRate') {
        assetPerformance.sort((a, b) => b.winRate - a.winRate);
      } else if (sortBy === 'tradeCount') {
        assetPerformance.sort((a, b) => b.tradeCount - a.tradeCount);
      } else if (sortBy === 'avgProfit') {
        assetPerformance.sort((a, b) => b.avgProfit - a.avgProfit);
      } else {
        // Default: totalProfit
        assetPerformance.sort((a, b) => b.totalProfit - a.totalProfit);
      }
      
      assetPerformance = assetPerformance.slice(0, limitNum);
      
      res.json({
        success: true,
        data: assetPerformance,
        count: assetPerformance.length,
        sortBy,
        limit: limitNum
      });
    } catch (error) {
      logger.error('Error getting top performing assets:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve top performing assets'
      });
    }
  });

  // Get recent trades
  router.get('/trades/recent', async (req, res) => {
    try {
      const { limit = '50' } = req.query;
      const limitNum = Math.min(parseInt(limit as string) || 50, 200);
      
      const trades = analyticsService.getTrades(limitNum);
      
      res.json({
        success: true,
        data: trades,
        count: trades.length,
        limit: limitNum
      });
    } catch (error) {
      logger.error('Error getting recent trades:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve recent trades'
      });
    }
  });

  // Get profit/loss distribution
  router.get('/pnl/distribution', async (req, res) => {
    try {
      const trades = analyticsService.getTrades(1000); // Get last 1000 trades
      
      const distribution = {
        profitable: 0,
        breakeven: 0,
        loss: 0,
        ranges: {
          'loss_high': 0,      // < -$100
          'loss_medium': 0,    // -$100 to -$10
          'loss_low': 0,       // -$10 to $0
          'profit_low': 0,     // $0 to $10
          'profit_medium': 0,  // $10 to $100
          'profit_high': 0     // > $100
        }
      };

      trades.forEach(trade => {
        const profit = trade.executedProfit;
        
        if (profit > 0) {
          distribution.profitable++;
          if (profit > 100) distribution.ranges.profit_high++;
          else if (profit > 10) distribution.ranges.profit_medium++;
          else distribution.ranges.profit_low++;
        } else if (profit < 0) {
          distribution.loss++;
          if (profit < -100) distribution.ranges.loss_high++;
          else if (profit < -10) distribution.ranges.loss_medium++;
          else distribution.ranges.loss_low++;
        } else {
          distribution.breakeven++;
        }
      });

      res.json({
        success: true,
        data: distribution,
        totalTrades: trades.length
      });
    } catch (error) {
      logger.error('Error getting P&L distribution:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve P&L distribution'
      });
    }
  });

  // Get analytics summary
  router.get('/summary', async (req, res) => {
    try {
      const overallMetrics = analyticsService.getOverallMetrics();
      const strategyMetrics = analyticsService.getStrategyMetrics();
      const assetPerformance = analyticsService.getAssetPerformance().slice(0, 5); // Top 5 assets
      const stats = analyticsService.getStats();

      const summary = {
        overall: overallMetrics,
        strategies: strategyMetrics,
        topAssets: assetPerformance,
        systemStats: stats,
        timestamp: Date.now()
      };

      res.json({
        success: true,
        data: summary
      });
    } catch (error) {
      logger.error('Error getting analytics summary:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve analytics summary'
      });
    }
  });

  // Get performance comparison
  router.get('/performance/compare', async (req, res) => {
    try {
      const { period1 = '7', period2 = '7' } = req.query;
      const period1Days = parseInt(period1 as string);
      const period2Days = parseInt(period2 as string);
      
      if (isNaN(period1Days) || isNaN(period2Days) || period1Days <= 0 || period2Days <= 0) {
        return res.status(400).json({
          success: false,
          error: 'Invalid period parameters'
        });
      }

      const now = Date.now();
      const period1Start = now - period1Days * 24 * 60 * 60 * 1000;
      const period2Start = period1Start - period2Days * 24 * 60 * 60 * 1000;

      const allTrades = analyticsService.getTrades(10000); // Get many trades
      
      const period1Trades = allTrades.filter(trade => 
        trade.timestamp >= period1Start && trade.timestamp < now
      );
      
      const period2Trades = allTrades.filter(trade => 
        trade.timestamp >= period2Start && trade.timestamp < period1Start
      );

      const calculateMetrics = (trades: any[]) => {
        const successful = trades.filter(t => t.executedProfit > 0);
        const totalProfit = successful.reduce((sum, t) => sum + t.executedProfit, 0);
        const totalLoss = trades.filter(t => t.executedProfit < 0)
          .reduce((sum, t) => sum + Math.abs(t.executedProfit), 0);
        
        return {
          totalTrades: trades.length,
          successfulTrades: successful.length,
          winRate: trades.length > 0 ? (successful.length / trades.length) * 100 : 0,
          totalProfit,
          totalLoss,
          netProfit: totalProfit - totalLoss,
          avgProfit: successful.length > 0 ? totalProfit / successful.length : 0
        };
      };

      const comparison = {
        period1: {
          days: period1Days,
          metrics: calculateMetrics(period1Trades)
        },
        period2: {
          days: period2Days,
          metrics: calculateMetrics(period2Trades)
        }
      };

      res.json({
        success: true,
        data: comparison
      });
    } catch (error) {
      logger.error('Error getting performance comparison:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve performance comparison'
      });
    }
  });

  // Get analytics statistics
  router.get('/stats', async (req, res) => {
    try {
      const stats = analyticsService.getStats();
      
      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      logger.error('Error getting analytics stats:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve analytics statistics'
      });
    }
  });

  return router;
}
