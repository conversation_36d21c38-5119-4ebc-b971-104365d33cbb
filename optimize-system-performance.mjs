#!/usr/bin/env node

import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  const timestamp = new Date().toISOString();
  console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
}

function logSection(title) {
  console.log('\n' + '='.repeat(80));
  log(title, 'cyan');
  console.log('='.repeat(80));
}

class SystemOptimizer {
  constructor() {
    this.optimizations = [];
    this.metrics = {};
  }

  async analyzeSystemPerformance() {
    logSection('📊 SYSTEM PERFORMANCE ANALYSIS');
    
    try {
      // Analyze current system metrics
      const response = await fetch('http://localhost:8080/api/system/metrics');
      const data = await response.json();
      
      if (data.success) {
        this.metrics = data.data;
        log('✅ System metrics retrieved successfully', 'green');
        
        // Analyze performance metrics
        this.analyzePerformanceMetrics();
        this.analyzeCacheMetrics();
        this.analyzeDatabaseMetrics();
        this.analyzeWebSocketMetrics();
        
      } else {
        throw new Error('Failed to retrieve system metrics');
      }
    } catch (error) {
      log(`❌ Performance analysis failed: ${error.message}`, 'red');
      throw error;
    }
  }

  analyzePerformanceMetrics() {
    log('🔍 Analyzing performance metrics...', 'blue');
    
    if (this.metrics.performance) {
      const perf = this.metrics.performance;
      
      // Check latency
      if (perf.averageLatency > 100) {
        this.optimizations.push({
          type: 'performance',
          priority: 'high',
          issue: 'High average latency',
          current: `${perf.averageLatency}ms`,
          target: '<100ms',
          recommendations: [
            'Enable connection pooling optimization',
            'Implement query result caching',
            'Optimize database indexes',
            'Consider read replicas for heavy queries'
          ]
        });
      }
      
      // Check system uptime
      if (perf.systemUptime < 99) {
        this.optimizations.push({
          type: 'reliability',
          priority: 'critical',
          issue: 'Low system uptime',
          current: `${perf.systemUptime}%`,
          target: '>99%',
          recommendations: [
            'Implement health check improvements',
            'Add automatic service restart mechanisms',
            'Enhance error handling and recovery',
            'Set up monitoring alerts'
          ]
        });
      }
      
      log(`   • Average Latency: ${perf.averageLatency || 0}ms`, 
          (perf.averageLatency || 0) < 100 ? 'green' : 'yellow');
      log(`   • System Uptime: ${perf.systemUptime || 0}%`, 
          (perf.systemUptime || 0) > 99 ? 'green' : 'red');
    }
  }

  analyzeCacheMetrics() {
    log('🔍 Analyzing cache performance...', 'blue');
    
    if (this.metrics.cache) {
      const cache = this.metrics.cache;
      
      // Check hit ratio
      if (cache.hitRatio < 80) {
        this.optimizations.push({
          type: 'cache',
          priority: 'medium',
          issue: 'Low cache hit ratio',
          current: `${cache.hitRatio}%`,
          target: '>80%',
          recommendations: [
            'Increase cache TTL for stable data',
            'Implement cache warming strategies',
            'Optimize cache key patterns',
            'Add cache preloading for frequently accessed data'
          ]
        });
      }
      
      // Check memory usage
      if (cache.memoryUsage > 400) { // 400MB threshold
        this.optimizations.push({
          type: 'memory',
          priority: 'medium',
          issue: 'High cache memory usage',
          current: `${cache.memoryUsage}MB`,
          target: '<400MB',
          recommendations: [
            'Implement LRU eviction policies',
            'Reduce cache entry sizes',
            'Optimize data serialization',
            'Consider cache partitioning'
          ]
        });
      }
      
      log(`   • Hit Ratio: ${cache.hitRatio || 0}%`, 
          (cache.hitRatio || 0) > 80 ? 'green' : 'yellow');
      log(`   • Memory Usage: ${cache.memoryUsage || 0}MB`, 
          (cache.memoryUsage || 0) < 400 ? 'green' : 'yellow');
      log(`   • Total Requests: ${cache.totalRequests || 0}`);
    }
  }

  analyzeDatabaseMetrics() {
    log('🔍 Analyzing database performance...', 'blue');
    
    if (this.metrics.database) {
      Object.entries(this.metrics.database).forEach(([dbName, dbHealth]) => {
        if (dbHealth.latency > 100) {
          this.optimizations.push({
            type: 'database',
            priority: 'high',
            issue: `High ${dbName} latency`,
            current: `${dbHealth.latency}ms`,
            target: '<100ms',
            recommendations: [
              'Optimize database queries',
              'Add appropriate indexes',
              'Consider connection pooling adjustments',
              'Implement query result caching'
            ]
          });
        }
        
        if (!dbHealth.isHealthy) {
          this.optimizations.push({
            type: 'database',
            priority: 'critical',
            issue: `${dbName} unhealthy`,
            current: 'Unhealthy',
            target: 'Healthy',
            recommendations: [
              'Check database connectivity',
              'Verify credentials and permissions',
              'Monitor resource usage',
              'Implement circuit breaker patterns'
            ]
          });
        }
        
        const healthIcon = dbHealth.isHealthy ? '✅' : '❌';
        const latencyColor = dbHealth.latency < 100 ? 'green' : 'yellow';
        log(`   ${healthIcon} ${dbName}: ${dbHealth.latency}ms`, latencyColor);
      });
    }
  }

  analyzeWebSocketMetrics() {
    log('🔍 Analyzing WebSocket performance...', 'blue');
    
    if (this.metrics.websocket) {
      const ws = this.metrics.websocket;
      
      // Check error rate
      const errorRate = ws.totalConnections > 0 ? (ws.errors / ws.totalConnections) * 100 : 0;
      if (errorRate > 5) {
        this.optimizations.push({
          type: 'websocket',
          priority: 'medium',
          issue: 'High WebSocket error rate',
          current: `${errorRate.toFixed(2)}%`,
          target: '<5%',
          recommendations: [
            'Implement better error handling',
            'Add connection retry mechanisms',
            'Optimize message serialization',
            'Monitor connection stability'
          ]
        });
      }
      
      log(`   • Active Connections: ${ws.activeConnections || 0}`);
      log(`   • Messages Sent: ${ws.messagesSent || 0}`);
      log(`   • Error Rate: ${errorRate.toFixed(2)}%`, errorRate < 5 ? 'green' : 'yellow');
    }
  }

  async generateOptimizationPlan() {
    logSection('🎯 OPTIMIZATION RECOMMENDATIONS');
    
    if (this.optimizations.length === 0) {
      log('🎉 No optimization issues found! System is performing optimally.', 'green');
      return;
    }
    
    // Sort by priority
    const priorityOrder = { 'critical': 0, 'high': 1, 'medium': 2, 'low': 3 };
    this.optimizations.sort((a, b) => priorityOrder[a.priority] - priorityOrder[b.priority]);
    
    log(`Found ${this.optimizations.length} optimization opportunities:`, 'yellow');
    
    this.optimizations.forEach((opt, index) => {
      const priorityColor = opt.priority === 'critical' ? 'red' : 
                           opt.priority === 'high' ? 'yellow' : 'blue';
      
      log(`\n${index + 1}. ${opt.issue} [${opt.priority.toUpperCase()}]`, priorityColor);
      log(`   Current: ${opt.current} → Target: ${opt.target}`);
      log(`   Recommendations:`);
      opt.recommendations.forEach(rec => {
        log(`     • ${rec}`, 'cyan');
      });
    });
  }

  async implementAutomaticOptimizations() {
    logSection('⚡ IMPLEMENTING AUTOMATIC OPTIMIZATIONS');
    
    const automaticOptimizations = [
      {
        name: 'Database Connection Pool Optimization',
        action: async () => {
          // This would adjust connection pool settings based on current load
          log('🔧 Optimizing database connection pools...', 'blue');
          // Implementation would go here
          return true;
        }
      },
      {
        name: 'Cache Configuration Tuning',
        action: async () => {
          log('🔧 Tuning cache configurations...', 'blue');
          // Implementation would go here
          return true;
        }
      },
      {
        name: 'Performance Monitoring Thresholds',
        action: async () => {
          log('🔧 Adjusting performance monitoring thresholds...', 'blue');
          // Implementation would go here
          return true;
        }
      }
    ];

    for (const optimization of automaticOptimizations) {
      try {
        const success = await optimization.action();
        if (success) {
          log(`✅ ${optimization.name} completed`, 'green');
        } else {
          log(`⚠️  ${optimization.name} partially completed`, 'yellow');
        }
      } catch (error) {
        log(`❌ ${optimization.name} failed: ${error.message}`, 'red');
      }
    }
  }

  async generateOptimizationReport() {
    logSection('📋 GENERATING OPTIMIZATION REPORT');
    
    const report = {
      timestamp: new Date().toISOString(),
      systemMetrics: this.metrics,
      optimizations: this.optimizations,
      summary: {
        totalIssues: this.optimizations.length,
        criticalIssues: this.optimizations.filter(o => o.priority === 'critical').length,
        highPriorityIssues: this.optimizations.filter(o => o.priority === 'high').length,
        mediumPriorityIssues: this.optimizations.filter(o => o.priority === 'medium').length,
        lowPriorityIssues: this.optimizations.filter(o => o.priority === 'low').length
      },
      recommendations: {
        immediate: this.optimizations.filter(o => o.priority === 'critical' || o.priority === 'high'),
        planned: this.optimizations.filter(o => o.priority === 'medium' || o.priority === 'low')
      }
    };

    const reportPath = path.join(__dirname, 'optimization-report.json');
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
    
    log(`📄 Optimization report saved: ${reportPath}`, 'green');
    
    // Display summary
    log('\n📊 OPTIMIZATION SUMMARY:', 'cyan');
    log(`   • Total Issues: ${report.summary.totalIssues}`);
    log(`   • Critical: ${report.summary.criticalIssues}`, 
        report.summary.criticalIssues > 0 ? 'red' : 'green');
    log(`   • High Priority: ${report.summary.highPriorityIssues}`, 
        report.summary.highPriorityIssues > 0 ? 'yellow' : 'green');
    log(`   • Medium Priority: ${report.summary.mediumPriorityIssues}`, 'blue');
    log(`   • Low Priority: ${report.summary.lowPriorityIssues}`, 'blue');

    return report;
  }

  async monitorSystemHealth(duration = 300000) { // 5 minutes default
    logSection('🔍 CONTINUOUS SYSTEM MONITORING');
    
    log(`Starting ${duration / 1000}s monitoring session...`, 'blue');
    
    const startTime = Date.now();
    const healthChecks = [];
    
    while (Date.now() - startTime < duration) {
      try {
        const response = await fetch('http://localhost:8080/health');
        const health = await response.json();
        
        if (health.success) {
          healthChecks.push({
            timestamp: Date.now(),
            status: health.data.status,
            score: health.data.score,
            services: health.data.services,
            databases: health.data.databases
          });
          
          const statusColor = health.data.status === 'healthy' ? 'green' : 
                             health.data.status === 'degraded' ? 'yellow' : 'red';
          log(`Health: ${health.data.status} (${health.data.score}/100)`, statusColor);
        }
        
        // Wait 30 seconds before next check
        await new Promise(resolve => setTimeout(resolve, 30000));
        
      } catch (error) {
        log(`⚠️  Health check failed: ${error.message}`, 'yellow');
        await new Promise(resolve => setTimeout(resolve, 30000));
      }
    }
    
    // Analyze monitoring results
    const avgScore = healthChecks.reduce((sum, check) => sum + check.score, 0) / healthChecks.length;
    const healthyChecks = healthChecks.filter(check => check.status === 'healthy').length;
    const healthyPercentage = (healthyChecks / healthChecks.length) * 100;
    
    log(`\n📊 MONITORING RESULTS:`, 'cyan');
    log(`   • Average Health Score: ${avgScore.toFixed(2)}/100`);
    log(`   • Healthy Checks: ${healthyPercentage.toFixed(2)}%`);
    log(`   • Total Checks: ${healthChecks.length}`);
    
    return {
      duration: duration,
      checks: healthChecks.length,
      averageScore: avgScore,
      healthyPercentage: healthyPercentage
    };
  }
}

async function main() {
  try {
    console.log(`${colors.bright}${colors.magenta}`);
    console.log('╔══════════════════════════════════════════════════════════════════════════════╗');
    console.log('║                        SYSTEM OPTIMIZATION SUITE                            ║');
    console.log('║                     Enhanced MEV Arbitrage Bot                              ║');
    console.log('╚══════════════════════════════════════════════════════════════════════════════╝');
    console.log(colors.reset);

    const optimizer = new SystemOptimizer();

    // Check if server is running
    try {
      await fetch('http://localhost:8080/health');
    } catch (error) {
      log('❌ Enhanced server is not running. Please start it first:', 'red');
      log('   npm run start:enhanced-system', 'yellow');
      process.exit(1);
    }

    // Step 1: Analyze current performance
    await optimizer.analyzeSystemPerformance();

    // Step 2: Generate optimization recommendations
    await optimizer.generateOptimizationPlan();

    // Step 3: Implement automatic optimizations
    await optimizer.implementAutomaticOptimizations();

    // Step 4: Generate comprehensive report
    const report = await optimizer.generateOptimizationReport();

    // Step 5: Optional continuous monitoring
    const args = process.argv.slice(2);
    if (args.includes('--monitor')) {
      const duration = args.includes('--duration') ? 
        parseInt(args[args.indexOf('--duration') + 1]) * 1000 : 300000;
      await optimizer.monitorSystemHealth(duration);
    }

    logSection('🎉 OPTIMIZATION COMPLETE');
    
    if (report.summary.criticalIssues > 0) {
      log(`⚠️  ${report.summary.criticalIssues} critical issues require immediate attention`, 'red');
      process.exit(1);
    } else if (report.summary.totalIssues > 0) {
      log(`✅ System is stable with ${report.summary.totalIssues} optimization opportunities`, 'yellow');
    } else {
      log('🎉 System is performing optimally!', 'green');
    }

  } catch (error) {
    log(`❌ Optimization failed: ${error.message}`, 'red');
    console.error(error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  log('\n🛑 Optimization interrupted by user', 'yellow');
  process.exit(1);
});

// Run the optimization
main();
