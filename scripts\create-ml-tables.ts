#!/usr/bin/env node

/**
 * Create ML Database Tables Script
 * 
 * This script creates the required ML tables in Supabase using direct SQL execution.
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config();

async function createMLTables() {
  console.log('🚀 Creating ML Database Tables...');

  if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
    console.error('❌ Supabase configuration missing');
    process.exit(1);
  }

  const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
  );

  try {
    // Create strategy_performance table using direct table creation
    console.log('📊 Creating strategy_performance table...');

    // First, try to create the table by inserting a dummy record and then deleting it
    // This will create the table structure automatically
    try {
      const { error: createError } = await supabase
        .from('strategy_performance')
        .insert({
          strategy_name: 'test',
          success: true,
          market_regime: 'normal'
        });

      if (createError && createError.message.includes('relation "strategy_performance" does not exist')) {
        console.log('⚠️ strategy_performance table does not exist. Please create it manually in Supabase SQL editor.');
        console.log('SQL to create table:');
        console.log(`
CREATE TABLE strategy_performance (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  strategy_name VARCHAR(100) NOT NULL,
  opportunity_id VARCHAR(100),
  execution_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  profit_usd DECIMAL(20, 8),
  gas_cost_usd DECIMAL(20, 8),
  success BOOLEAN NOT NULL,
  market_regime VARCHAR(50),
  confidence_score DECIMAL(5, 4),
  execution_duration_ms INTEGER,
  slippage_percentage DECIMAL(8, 6),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
        `);
      } else {
        // Delete the test record
        await supabase
          .from('strategy_performance')
          .delete()
          .eq('strategy_name', 'test');
        console.log('✅ strategy_performance table exists and is accessible');
      }
    } catch (error) {
      console.log('⚠️ Could not verify strategy_performance table');
    }

    // Create strategy_weights table
    console.log('📊 Creating strategy_weights table...');
    const { error: weightsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS strategy_weights (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          strategy_name VARCHAR(100) NOT NULL,
          market_regime VARCHAR(50) NOT NULL,
          weight DECIMAL(8, 6) NOT NULL CHECK (weight >= 0 AND weight <= 1),
          confidence DECIMAL(5, 4) NOT NULL CHECK (confidence >= 0 AND confidence <= 1),
          last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          performance_samples INTEGER DEFAULT 0,
          avg_profit_usd DECIMAL(20, 8) DEFAULT 0,
          success_rate DECIMAL(5, 4) DEFAULT 0,
          UNIQUE(strategy_name, market_regime)
        );

        CREATE INDEX IF NOT EXISTS idx_strategy_weights_strategy ON strategy_weights(strategy_name);
        CREATE INDEX IF NOT EXISTS idx_strategy_weights_regime ON strategy_weights(market_regime);
      `
    });

    if (weightsError) {
      console.log('⚠️ strategy_weights table might already exist or using direct SQL...');
    } else {
      console.log('✅ strategy_weights table created');
    }

    // Create market_regimes table
    console.log('📊 Creating market_regimes table...');
    const { error: regimesError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS market_regimes (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          regime_name VARCHAR(50) UNIQUE NOT NULL,
          description TEXT,
          volatility_threshold DECIMAL(8, 6),
          volume_threshold DECIMAL(20, 8),
          trend_strength DECIMAL(5, 4),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    });

    if (regimesError) {
      console.log('⚠️ market_regimes table might already exist or using direct SQL...');
    } else {
      console.log('✅ market_regimes table created');
    }

    // Create ml_training_data table
    console.log('📊 Creating ml_training_data table...');
    const { error: trainingError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS ml_training_data (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          opportunity_id VARCHAR(100) NOT NULL,
          features JSONB NOT NULL,
          target_profit DECIMAL(20, 8),
          actual_profit DECIMAL(20, 8),
          market_regime VARCHAR(50),
          timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          strategy_used VARCHAR(100),
          success BOOLEAN
        );

        CREATE INDEX IF NOT EXISTS idx_ml_training_data_timestamp ON ml_training_data(timestamp);
        CREATE INDEX IF NOT EXISTS idx_ml_training_data_regime ON ml_training_data(market_regime);
      `
    });

    if (trainingError) {
      console.log('⚠️ ml_training_data table might already exist or using direct SQL...');
    } else {
      console.log('✅ ml_training_data table created');
    }

    // Create strategy_learning_events table
    console.log('📊 Creating strategy_learning_events table...');
    const { error: eventsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS strategy_learning_events (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          event_type VARCHAR(50) NOT NULL,
          strategy_name VARCHAR(100),
          market_regime VARCHAR(50),
          old_weight DECIMAL(8, 6),
          new_weight DECIMAL(8, 6),
          confidence_change DECIMAL(5, 4),
          trigger_reason TEXT,
          performance_data JSONB,
          timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        CREATE INDEX IF NOT EXISTS idx_strategy_learning_events_timestamp ON strategy_learning_events(timestamp);
        CREATE INDEX IF NOT EXISTS idx_strategy_learning_events_strategy ON strategy_learning_events(strategy_name);
      `
    });

    if (eventsError) {
      console.log('⚠️ strategy_learning_events table might already exist or using direct SQL...');
    } else {
      console.log('✅ strategy_learning_events table created');
    }

    // Insert initial market regimes
    console.log('📊 Inserting initial market regimes...');
    const regimes = [
      { regime_name: 'normal', description: 'Normal market conditions', volatility_threshold: 0.02, volume_threshold: 1000000, trend_strength: 0.5 },
      { regime_name: 'high_volatility', description: 'High volatility market', volatility_threshold: 0.05, volume_threshold: 2000000, trend_strength: 0.7 },
      { regime_name: 'low_volume', description: 'Low volume market', volatility_threshold: 0.01, volume_threshold: 500000, trend_strength: 0.3 },
      { regime_name: 'trending_up', description: 'Strong upward trend', volatility_threshold: 0.03, volume_threshold: 1500000, trend_strength: 0.8 },
      { regime_name: 'trending_down', description: 'Strong downward trend', volatility_threshold: 0.03, volume_threshold: 1500000, trend_strength: 0.8 }
    ];

    for (const regime of regimes) {
      const { error } = await supabase
        .from('market_regimes')
        .upsert(regime, { onConflict: 'regime_name' });
      
      if (error && !error.message.includes('duplicate key')) {
        console.log(`⚠️ Could not insert regime ${regime.regime_name}:`, error.message);
      }
    }

    console.log('✅ Initial market regimes inserted');

    // Create analytics view
    console.log('📊 Creating analytics view...');
    const { error: viewError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE OR REPLACE VIEW strategy_performance_summary AS
        SELECT 
          sp.strategy_name,
          sp.market_regime,
          COUNT(*) as total_executions,
          COUNT(*) FILTER (WHERE sp.success = true) as successful_executions,
          ROUND(COUNT(*) FILTER (WHERE sp.success = true)::decimal / COUNT(*) * 100, 2) as success_rate,
          ROUND(AVG(sp.profit_usd), 4) as avg_profit_usd,
          ROUND(SUM(sp.profit_usd), 4) as total_profit_usd,
          ROUND(AVG(sp.gas_cost_usd), 4) as avg_gas_cost_usd,
          ROUND(AVG(sp.execution_duration_ms), 2) as avg_execution_time_ms,
          ROUND(AVG(sp.confidence_score), 4) as avg_confidence,
          MIN(sp.execution_time) as first_execution,
          MAX(sp.execution_time) as last_execution
        FROM strategy_performance sp
        GROUP BY sp.strategy_name, sp.market_regime
        ORDER BY total_profit_usd DESC;
      `
    });

    if (viewError) {
      console.log('⚠️ Analytics view might already exist or using direct SQL...');
    } else {
      console.log('✅ Analytics view created');
    }

    console.log('🎉 ML Database Tables setup completed successfully!');
    console.log('');
    console.log('📋 Summary:');
    console.log('  - strategy_performance: Records all strategy execution attempts');
    console.log('  - strategy_weights: Stores ML-learned strategy weights');
    console.log('  - market_regimes: Defines different market conditions');
    console.log('  - ml_training_data: Stores normalized features for ML models');
    console.log('  - strategy_learning_events: Logs all learning decisions');
    console.log('  - strategy_performance_summary: Analytics view for performance tracking');
    console.log('');
    console.log('🎯 The ML learning system is now ready to start learning from trade executions!');

  } catch (error) {
    console.error('❌ Error creating ML tables:', error);
    process.exit(1);
  }
}

// Run the script
createMLTables().catch(console.error);
