// Ultra-simple backend for testing
import http from 'http';

console.log('🚀 Starting ultra-simple backend...');

const server = http.createServer((req, res) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);

  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Content-Type', 'application/json');

  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  let response = {};

  if (req.url === '/health') {
    response = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    };
  } else if (req.url === '/api/opportunities') {
    response = {
      success: true,
      data: [
        {
          id: 'opp_1',
          type: 'intra-chain',
          assets: ['ETH', 'USDC'],
          exchanges: ['Uniswap', 'SushiSwap'],
          potentialProfit: 125.50,
          timestamp: Date.now(),
          network: 'ethereum'
        },
        {
          id: 'opp_2',
          type: 'triangular',
          assets: ['WBTC', 'ETH', 'USDC'],
          exchanges: ['Balancer', 'Curve'],
          potentialProfit: 89.25,
          timestamp: Date.now() - 30000,
          network: 'ethereum'
        }
      ]
    };
  } else if (req.url === '/api/trades') {
    response = {
      success: true,
      data: [
        {
          id: 'trade_1',
          type: 'intra-chain',
          assets: ['ETH', 'USDC'],
          exchanges: ['Uniswap', 'SushiSwap'],
          executedProfit: 118.25,
          gasFees: 7.25,
          status: 'success',
          timestamp: Date.now() - 300000,
          network: 'ethereum'
        }
      ]
    };
  } else if (req.url === '/api/tokens') {
    response = {
      success: true,
      data: [
        {
          id: 'eth',
          name: 'Ethereum',
          symbol: 'ETH',
          network: 'ethereum',
          safetyScore: 100
        },
        {
          id: 'usdc',
          name: 'USD Coin',
          symbol: 'USDC',
          network: 'ethereum',
          safetyScore: 95
        }
      ]
    };
  } else if (req.url === '/api/analytics/performance') {
    response = {
      success: true,
      data: {
        totalTrades: 45,
        netProfit: 2650.50,
        winRate: 86.7,
        dailyVolume: 125000
      }
    };
  } else {
    res.writeHead(404);
    response = { error: 'Not found' };
  }

  res.writeHead(200);
  res.end(JSON.stringify(response, null, 2));
});

const PORT = 3001;
server.listen(PORT, () => {
  console.log(`✅ Backend running on http://localhost:${PORT}`);
  console.log(`✅ Health: http://localhost:${PORT}/health`);
  console.log(`✅ Ready to serve requests!`);
});

server.on('error', (err) => {
  console.error('❌ Server error:', err.message);
});
