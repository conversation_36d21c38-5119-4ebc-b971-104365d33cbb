#!/usr/bin/env node

// Simple startup script for MEV Arbitrage Bot
import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { existsSync } from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🚀 MEV Arbitrage Bot - Simplified System');
console.log('=====================================\n');

// Check if backend file exists
const backendFile = join(__dirname, 'working-backend.mjs');
const frontendFile = join(__dirname, 'index.html');

if (!existsSync(backendFile)) {
    console.error('❌ Backend file not found:', backendFile);
    process.exit(1);
}

if (!existsSync(frontendFile)) {
    console.error('❌ Frontend file not found:', frontendFile);
    process.exit(1);
}

console.log('✅ Backend file found:', backendFile);
console.log('✅ Frontend file found:', frontendFile);
console.log('');

// Start backend
console.log('📡 Starting backend server...');
const backend = spawn('node', ['working-backend.mjs'], {
    cwd: __dirname,
    stdio: 'pipe'
});

backend.stdout.on('data', (data) => {
    console.log(`[BACKEND] ${data.toString().trim()}`);
});

backend.stderr.on('data', (data) => {
    console.error(`[BACKEND ERROR] ${data.toString().trim()}`);
});

backend.on('close', (code) => {
    console.log(`\n❌ Backend process exited with code ${code}`);
    process.exit(code);
});

// Wait for backend to start
setTimeout(() => {
    console.log('\n🌐 Frontend Information:');
    console.log('========================');
    console.log(`📄 Frontend file: ${frontendFile}`);
    console.log(`🔗 Open in browser: file://${frontendFile}`);
    console.log('');
    console.log('📊 System URLs:');
    console.log('===============');
    console.log('🔧 Backend API: http://localhost:3001');
    console.log('❤️  Health Check: http://localhost:3001/health');
    console.log('📈 Opportunities: http://localhost:3001/api/opportunities');
    console.log('💰 Trades: http://localhost:3001/api/trades');
    console.log('🪙 Tokens: http://localhost:3001/api/tokens');
    console.log('📊 Analytics: http://localhost:3001/api/analytics/performance');
    console.log('');
    console.log('🎯 To use the system:');
    console.log('1. Backend is running automatically');
    console.log('2. Open index.html in your browser');
    console.log('3. The frontend will connect to the backend automatically');
    console.log('');
    console.log('Press Ctrl+C to stop the backend server.');
}, 3000);

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down MEV Arbitrage Bot...');
    backend.kill();
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Shutting down MEV Arbitrage Bot...');
    backend.kill();
    process.exit(0);
});
