import { z } from 'zod';

const configSchema = z.object({
  // Server Configuration
  PORT: z.string().default('3001'),
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),

  // Database Configuration
  REDIS_URL: z.string().default('redis://localhost:6379'),
  DATABASE_URL: z.string().optional(),

  // Supabase Configuration
  SUPABASE_URL: z.string().optional(),
  SUPABASE_ANON_KEY: z.string().optional(),
  SUPABASE_SERVICE_ROLE_KEY: z.string().optional(),
  SUPABASE_JWT_SECRET: z.string().optional(),
  SUPABASE_DATABASE_URL: z.string().optional(),

  // InfluxDB Configuration
  INFLUXDB_URL: z.string().default('http://localhost:8086'),
  INFLUXDB_TOKEN: z.string().optional(),
  INFLUXDB_ORG: z.string().default('mev-arbitrage-org'),
  INFLUXDB_BUCKET: z.string().default('mev-arbitrage-metrics'),
  INFLUXDB_USERNAME: z.string().optional(),
  INFLUXDB_PASSWORD: z.string().optional(),

  // Blockchain Configuration
  ETHEREUM_RPC_URL: z.string().default('https://eth-mainnet.alchemyapi.io/v2/your-api-key'),
  POLYGON_RPC_URL: z.string().default('https://polygon-mainnet.alchemyapi.io/v2/your-api-key'),
  BSC_RPC_URL: z.string().default('https://bsc-dataseed.binance.org/'),
  SOLANA_RPC_URL: z.string().default('https://api.mainnet-beta.solana.com'),

  // Private Keys (for transaction signing)
  PRIVATE_KEY: z.string().optional(),
  FLASHBOTS_PRIVATE_KEY: z.string().optional(),

  // External API Keys
  CHAINLINK_API_KEY: z.string().optional(),
  PYTH_API_KEY: z.string().optional(),
  COINGECKO_API_KEY: z.string().optional(),
  ETHERSCAN_API_KEY: z.string().optional(),
  POLYGONSCAN_API_KEY: z.string().optional(),
  BSCSCAN_API_KEY: z.string().optional(),
  ALCHEMY_API_KEY: z.string().optional(),
  INFURA_PROJECT_ID: z.string().optional(),
  MORALIS_API_KEY: z.string().optional(),

  // Trading Configuration
  MIN_PROFIT_THRESHOLD: z.string().default('50'), // USD
  MAX_POSITION_SIZE: z.string().default('10000'), // USD
  MAX_SLIPPAGE: z.string().default('0.5'), // percentage
  GAS_PRICE_MULTIPLIER: z.string().default('1.1'),

  // Pre-Execution Validation Configuration
  ENABLE_MANDATORY_SIMULATION: z.string().default('true'),
  MIN_PROFIT_MARGIN_BUFFER: z.string().default('10'), // percentage above break-even
  MAX_SIMULATION_TIME: z.string().default('5000'), // milliseconds
  SIMULATION_SLIPPAGE_BUFFER: z.string().default('0.2'), // additional slippage buffer
  ENABLE_BRIDGE_FEE_CALCULATION: z.string().default('true'),
  ENABLE_NETWORK_CONGESTION_CHECK: z.string().default('true'),

  // Risk Management
  EMERGENCY_STOP: z.string().default('false'),
  MAX_DAILY_LOSS: z.string().default('1000'), // USD
  POSITION_SIZE_PERCENTAGE: z.string().default('2'), // percentage of total capital

  // MEV Protection Configuration
  ENABLE_MEV_PROTECTION: z.string().default('true'),
  FLASHBOTS_RELAY_URL: z.string().default('https://relay.flashbots.net'),
  FLASHBOTS_BUILDER_URL: z.string().default('https://builder.flashbots.net'),
  FLASHBOTS_PROTECT_URL: z.string().default('https://protect.flashbots.net'),
  MEV_PROTECTION_TIMEOUT: z.string().default('30000'), // milliseconds
  ENABLE_PRIVATE_MEMPOOL: z.string().default('true'),
  DYNAMIC_GAS_PRICING: z.string().default('true'),
  MEV_PROTECTION_FALLBACK: z.string().default('true'),

  // Telegram Bot Configuration
  TELEGRAM_BOT_TOKEN: z.string().optional(),
  TELEGRAM_CHAT_ID: z.string().optional(),

  // Email Notifications
  SMTP_HOST: z.string().default('smtp.gmail.com'),
  SMTP_PORT: z.string().default('587'),
  SMTP_USER: z.string().optional(),
  SMTP_PASS: z.string().optional(),
  ALERT_EMAIL: z.string().optional(),

  // Security
  JWT_SECRET: z.string().optional(),
  API_RATE_LIMIT_WINDOW: z.string().default('900000'), // 15 minutes
  API_RATE_LIMIT_MAX: z.string().default('100'),
  CORS_ORIGIN: z.string().default('http://localhost:5173,http://localhost:3000'),

  // Monitoring
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  ENABLE_METRICS: z.string().default('true'),

  // Enhanced Service Configuration
  ENABLE_PROFIT_VALIDATION: z.string().default('true'),
  ENABLE_ENHANCED_TOKEN_MONITORING: z.string().default('true'),
  ENABLE_FLASH_LOANS: z.string().default('true'),
  ENABLE_EXECUTION_QUEUE: z.string().default('true'),

  // Profit Validation Configuration
  PROFIT_VALIDATION_THRESHOLD: z.string().default('0.01'), // 1% minimum profit
  PROFIT_VALIDATION_TIMEOUT: z.string().default('3000'), // milliseconds
  ENABLE_POST_EXECUTION_VALIDATION: z.string().default('true'),
  PROFIT_ACCURACY_THRESHOLD: z.string().default('90'), // percentage

  // Enhanced Token Monitoring Configuration
  TOKEN_MONITORING_INTERVAL: z.string().default('1000'), // milliseconds
  MAX_PRICE_UPDATE_LATENCY: z.string().default('5000'), // milliseconds
  ENABLE_MULTI_CHAIN_MONITORING: z.string().default('true'),
  TOP_TOKENS_COUNT: z.string().default('50'),

  // Flash Loan Configuration
  FLASH_LOAN_PROVIDERS: z.string().default('aave_v3,balancer_v2,dydx,uniswap_v3'),
  FLASH_LOAN_MAX_AMOUNT: z.string().default('50000000'), // $50M
  FLASH_LOAN_MIN_AMOUNT: z.string().default('1'), // $1
  FLASH_LOAN_GAS_OVERHEAD: z.string().default('150000'),

  // Execution Queue Configuration
  QUEUE_MAX_SIZE: z.string().default('1000'),
  QUEUE_PROCESSING_INTERVAL: z.string().default('100'), // milliseconds
  QUEUE_PRIORITY_DECAY_RATE: z.string().default('0.95'),
  QUEUE_MAX_WAIT_TIME: z.string().default('30000'), // milliseconds

  // Performance Targets
  TARGET_UPTIME_PERCENTAGE: z.string().default('99'),
  MAX_SERVICE_LATENCY: z.string().default('1000'), // milliseconds
  MAX_QUEUE_PROCESSING_TIME: z.string().default('500'), // milliseconds
  TARGET_PRICE_UPDATE_FREQUENCY: z.string().default('5000'), // milliseconds

  // Execution Queue Configuration
  MAX_QUEUE_SIZE: z.string().default('100'),
  MAX_OPPORTUNITY_AGE: z.string().default('30000'), // milliseconds
  QUEUE_REORDER_FREQUENCY: z.string().default('5000'), // milliseconds
  QUEUE_CLEANUP_FREQUENCY: z.string().default('10000'), // milliseconds

  // Priority Weights Configuration
  NET_PROFIT_WEIGHT: z.string().default('0.4'),
  PROFIT_MARGIN_WEIGHT: z.string().default('0.25'),
  CONFIDENCE_WEIGHT: z.string().default('0.2'),
  TIME_SENSITIVITY_WEIGHT: z.string().default('0.1'),
  RISK_ADJUSTMENT_WEIGHT: z.string().default('0.05'),

  // Execution Capacity Configuration
  MAX_CONCURRENT_TRADES: z.string().default('3'),
  MAX_RISK_CAPACITY: z.string().default('100000'),

  // Database Connection Pool Configuration
  SUPABASE_POOL_MAX: z.string().default('20'),
  SUPABASE_POOL_IDLE_TIMEOUT: z.string().default('30000'),
  INFLUXDB_POOL_MAX: z.string().default('10'),
  INFLUXDB_POOL_IDLE_TIMEOUT: z.string().default('60000'),
  REDIS_POOL_MAX: z.string().default('15'),
  POSTGRES_POOL_MAX: z.string().default('15'),
  POSTGRES_POOL_IDLE_TIMEOUT: z.string().default('30000'),

  // Circuit Breaker Configuration
  CIRCUIT_BREAKER_FAILURE_THRESHOLD: z.string().default('5'),
  CIRCUIT_BREAKER_TIMEOUT: z.string().default('3000'),
  CIRCUIT_BREAKER_RESET_TIMEOUT: z.string().default('30000'),
  CIRCUIT_BREAKER_MONITORING_PERIOD: z.string().default('60000'),

  // Performance Targets & Monitoring
  MAX_SERVICE_LATENCY: z.string().default('1000'),
  MAX_DATABASE_QUERY_TIME: z.string().default('100'),
  MAX_API_RESPONSE_TIME: z.string().default('500'),
  MAX_QUEUE_OPERATION_TIME: z.string().default('500'),
  TARGET_UPTIME_PERCENTAGE: z.string().default('99'),
  CACHE_HIT_RATIO_THRESHOLD: z.string().default('80'),

  // WebSocket Configuration
  WEBSOCKET_HEARTBEAT_INTERVAL: z.string().default('30000'),
  WEBSOCKET_RECONNECT_ATTEMPTS: z.string().default('5'),
  WEBSOCKET_RECONNECT_DELAY: z.string().default('1000'),
  WEBSOCKET_MAX_CONNECTIONS: z.string().default('100'),

  // Caching Configuration
  REDIS_DEFAULT_TTL: z.string().default('30'),
  BROWSER_CACHE_TTL: z.string().default('300'),
  OPPORTUNITY_CACHE_TTL: z.string().default('60'),
  PRICE_CACHE_TTL: z.string().default('30'),
  QUEUE_CACHE_TTL: z.string().default('120'),
  HEALTH_CACHE_TTL: z.string().default('15'),

  // Data Retention Configuration
  INFLUXDB_RAW_DATA_RETENTION: z.string().default('7d'),
  INFLUXDB_MINUTE_AGGREGATES_RETENTION: z.string().default('30d'),
  INFLUXDB_HOURLY_AGGREGATES_RETENTION: z.string().default('1y'),
  SUPABASE_DATA_CLEANUP_DAYS: z.string().default('90'),

  // Batch Processing Configuration
  INFLUXDB_BATCH_SIZE: z.string().default('1000'),
  INFLUXDB_BATCH_TIMEOUT: z.string().default('5000'),
  SUPABASE_BATCH_SIZE: z.string().default('50'),
  SUPABASE_BATCH_TIMEOUT: z.string().default('3000'),

  // Security & Rate Limiting
  API_RATE_LIMIT_PER_SECOND: z.string().default('10'),
  API_RATE_LIMIT_BURST: z.string().default('100'),
  CREDENTIAL_ROTATION_DAYS: z.string().default('90'),
  ACCESS_LOG_RETENTION_DAYS: z.string().default('30')
});

export type Config = z.infer<typeof configSchema>;

export const config: Config = configSchema.parse(process.env);

export default config;
