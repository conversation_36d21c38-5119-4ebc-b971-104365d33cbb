// Simple test to check if backend dependencies are working
import express from 'express';
import cors from 'cors';
import { WebSocketServer } from 'ws';
import { createClient } from 'redis';

console.log('Testing backend dependencies...');

try {
  console.log('✓ Express loaded');
  console.log('✓ CORS loaded');
  console.log('✓ WebSocket loaded');
  console.log('✓ Redis loaded');

  console.log('All dependencies loaded successfully!');

  // Test Redis connection
  const client = createClient({
    url: 'redis://localhost:6379'
  });

  client.on('error', (err) => {
    console.log('Redis connection error:', err.message);
  });

  client.on('connect', () => {
    console.log('✓ Redis connected successfully');
    client.quit();
  });

  client.connect().catch(console.error);

} catch (error) {
  console.error('Error loading dependencies:', error.message);
}
