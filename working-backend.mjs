// Working backend server for MEV Arbitrage Bot
import express from 'express';
import cors from 'cors';

console.log('🚀 Starting MEV Arbitrage Bot backend...');

const app = express();

// Middleware
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:3000'],
  credentials: true
}));
app.use(express.json());

// Request logging
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Mock data
const mockOpportunities = [
  {
    id: 'opp_1',
    type: 'intra-chain',
    assets: ['ETH', 'USDC'],
    exchanges: ['Uniswap', 'SushiSwap'],
    potentialProfit: 125.50,
    profitPercentage: 2.1,
    timestamp: Date.now(),
    network: 'ethereum',
    confidence: 85,
    slippage: 0.5
  },
  {
    id: 'opp_2',
    type: 'triangular',
    assets: ['WBTC', 'ETH', 'USDC'],
    exchanges: ['Balancer', 'Curve', 'Uniswap'],
    potentialProfit: 89.25,
    profitPercentage: 1.8,
    timestamp: Date.now() - 30000,
    network: 'ethereum',
    confidence: 78,
    slippage: 0.8
  },
  {
    id: 'opp_3',
    type: 'cross-chain',
    assets: ['USDC', 'USDT'],
    exchanges: ['Polygon-Uniswap', 'Ethereum-Curve'],
    potentialProfit: 45.75,
    profitPercentage: 0.9,
    timestamp: Date.now() - 60000,
    network: 'polygon',
    confidence: 92,
    slippage: 0.3
  }
];

const mockTrades = [
  {
    id: 'trade_1',
    type: 'intra-chain',
    assets: ['ETH', 'USDC'],
    exchanges: ['Uniswap', 'SushiSwap'],
    executedProfit: 118.25,
    gasFees: 7.25,
    status: 'success',
    timestamp: Date.now() - 300000,
    network: 'ethereum',
    txHash: '0x1234567890abcdef'
  },
  {
    id: 'trade_2',
    type: 'triangular',
    assets: ['WBTC', 'ETH', 'USDC'],
    exchanges: ['Balancer', 'Curve', 'Uniswap'],
    executedProfit: 82.50,
    gasFees: 12.75,
    status: 'success',
    timestamp: Date.now() - 600000,
    network: 'ethereum',
    txHash: '0xabcdef1234567890'
  },
  {
    id: 'trade_3',
    type: 'cross-chain',
    assets: ['USDC', 'USDT'],
    exchanges: ['Polygon-Uniswap', 'Ethereum-Curve'],
    executedProfit: 42.30,
    gasFees: 3.45,
    status: 'success',
    timestamp: Date.now() - 900000,
    network: 'polygon',
    txHash: '0x9876543210fedcba'
  }
];

const mockTokens = [
  {
    id: 'ethereum_******************************************',
    name: 'Ethereum',
    symbol: 'ETH',
    address: '******************************************',
    liquidity: 1000000,
    safetyScore: 100,
    isWhitelisted: true,
    network: 'ethereum',
    decimals: 18,
    totalSupply: '120000000000000000000000000',
    lastUpdated: Date.now()
  },
  {
    id: 'ethereum_0xA0b86a33E6441b8C4505B8C4505B8C4505B8C4505',
    name: 'USD Coin',
    symbol: 'USDC',
    address: '0xA0b86a33E6441b8C4505B8C4505B8C4505B8C4505',
    liquidity: 500000,
    safetyScore: 95,
    isWhitelisted: true,
    network: 'ethereum',
    decimals: 6,
    totalSupply: '50000000000000',
    lastUpdated: Date.now()
  },
  {
    id: 'ethereum_******************************************',
    name: 'Wrapped Bitcoin',
    symbol: 'WBTC',
    address: '******************************************',
    liquidity: 750000,
    safetyScore: 98,
    isWhitelisted: true,
    network: 'ethereum',
    decimals: 8,
    totalSupply: '21000000000000',
    lastUpdated: Date.now()
  }
];

const mockMetrics = {
  totalTrades: 45,
  successfulTrades: 39,
  totalProfit: 2850.75,
  netProfit: 2650.50,
  winRate: 86.7,
  avgProfit: 67.96,
  dailyVolume: 125000
};

// Routes
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    services: {
      backend: true,
      websocket: false
    }
  });
});

app.get('/api/opportunities', (req, res) => {
  const limit = parseInt(req.query.limit) || 20;
  const limitedOpportunities = mockOpportunities.slice(0, limit);

  res.json({
    success: true,
    data: limitedOpportunities,
    count: limitedOpportunities.length
  });
});

app.get('/api/trades', (req, res) => {
  const limit = parseInt(req.query.limit) || 50;
  const limitedTrades = mockTrades.slice(0, limit);

  res.json({
    success: true,
    data: limitedTrades,
    count: limitedTrades.length
  });
});

app.get('/api/tokens', (req, res) => {
  res.json({
    success: true,
    data: mockTokens,
    count: mockTokens.length
  });
});

app.get('/api/analytics/performance', (req, res) => {
  res.json({
    success: true,
    data: mockMetrics
  });
});

app.get('/api/system/health', (req, res) => {
  res.json({
    success: true,
    data: {
      isHealthy: true,
      emergencyStop: false,
      riskMetrics: {
        totalExposure: 0,
        dailyPnL: 250.75,
        maxDrawdown: 0,
        volatility: 15.2,
        winRate: 86.7
      },
      activeAlerts: 0,
      criticalAlerts: 0
    }
  });
});

// Error handling
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: err.message
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route not found',
    path: req.url
  });
});

// Start server
const PORT = process.env.PORT || 8080;
app.listen(PORT, '127.0.0.1', () => {
  console.log(`✅ Backend server running on http://localhost:${PORT}`);
  console.log(`✅ Health check: http://localhost:${PORT}/health`);
  console.log(`✅ API endpoints available:`);
  console.log(`   - GET /api/opportunities`);
  console.log(`   - GET /api/trades`);
  console.log(`   - GET /api/tokens`);
  console.log(`   - GET /api/analytics/performance`);
  console.log(`   - GET /api/system/health`);
  console.log(`🔄 Ready to serve frontend requests...`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 Shutting down backend...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 Shutting down backend...');
  process.exit(0);
});
