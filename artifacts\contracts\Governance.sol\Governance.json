{"_format": "hh-sol-artifact-1", "contractName": "Governance", "sourceName": "contracts/Governance.sol", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "bool", "name": "stopped", "type": "bool"}, {"indexed": true, "internalType": "address", "name": "triggered<PERSON>y", "type": "address"}], "name": "EmergencyStopToggled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "string", "name": "name", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "oldValue", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newValue", "type": "uint256"}], "name": "ParameterUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "proposalId", "type": "uint256"}], "name": "ProposalCanceled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "proposalId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "proposer", "type": "address"}, {"indexed": false, "internalType": "string", "name": "description", "type": "string"}], "name": "ProposalCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "proposalId", "type": "uint256"}], "name": "ProposalExecuted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "proposalId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "voter", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "support", "type": "bool"}, {"indexed": false, "internalType": "uint256", "name": "weight", "type": "uint256"}], "name": "VoteCast", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "old<PERSON><PERSON>er", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newPower", "type": "uint256"}], "name": "VotingPowerUpdated", "type": "event"}, {"inputs": [{"internalType": "address", "name": "stopper", "type": "address"}], "name": "addEmergencyStopper", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}], "name": "cancelProposal", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}, {"internalType": "bool", "name": "support", "type": "bool"}], "name": "castVote", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "emergencyStop", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "emergencyStoppers", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}], "name": "executeProposal", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}], "name": "getParameter", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getParameterNames", "outputs": [{"internalType": "string[]", "name": "", "type": "string[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}], "name": "getProposal", "outputs": [{"internalType": "address", "name": "proposer", "type": "address"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "uint256", "name": "startTime", "type": "uint256"}, {"internalType": "uint256", "name": "endTime", "type": "uint256"}, {"internalType": "uint256", "name": "forVotes", "type": "uint256"}, {"internalType": "uint256", "name": "againstVotes", "type": "uint256"}, {"internalType": "bool", "name": "executed", "type": "bool"}, {"internalType": "bool", "name": "canceled", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "parameterNames", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "", "type": "string"}], "name": "parameters", "outputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "minValue", "type": "uint256"}, {"internalType": "uint256", "name": "maxValue", "type": "uint256"}, {"internalType": "uint256", "name": "lastUpdated", "type": "uint256"}, {"internalType": "address", "name": "updatedBy", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "proposalCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "proposalThreshold", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "proposals", "outputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "address", "name": "proposer", "type": "address"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "bytes", "name": "callData", "type": "bytes"}, {"internalType": "address", "name": "target", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "startTime", "type": "uint256"}, {"internalType": "uint256", "name": "endTime", "type": "uint256"}, {"internalType": "uint256", "name": "forVotes", "type": "uint256"}, {"internalType": "uint256", "name": "againstVotes", "type": "uint256"}, {"internalType": "bool", "name": "executed", "type": "bool"}, {"internalType": "bool", "name": "canceled", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "description", "type": "string"}, {"internalType": "address", "name": "target", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "callData", "type": "bytes"}], "name": "propose", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "quorum<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "stopper", "type": "address"}], "name": "removeEmergencyStopper", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "power", "type": "uint256"}], "name": "setVotingPower", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "toggleEmergencyStop", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "updateParameter", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "votingPeriod", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "voting<PERSON>ower", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}