// Final Database Connection Test
import { createClient } from '@supabase/supabase-js';
import { InfluxDB, Point } from '@influxdata/influxdb-client';
import dotenv from 'dotenv';

dotenv.config();

console.log('🎯 Final Database Connection Test\n');

async function testSupabase() {
  console.log('📊 Testing Supabase...');
  try {
    const supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      { auth: { autoRefreshToken: false, persistSession: false } }
    );

    // Test connection by querying configuration table
    const { data, error } = await supabase
      .from('configuration')
      .select('key')
      .limit(1);

    if (error) {
      console.log('❌ Supabase failed:', error.message);
      return false;
    }

    console.log('✅ Supabase connected successfully');
    console.log(`   URL: ${process.env.SUPABASE_URL}`);
    
    // Test all tables exist
    const tables = ['trades', 'opportunities', 'performance_metrics', 'tokens', 'system_alerts', 'configuration'];
    let tableCount = 0;
    
    for (const table of tables) {
      const { error: tableError } = await supabase.from(table).select('*').limit(1);
      if (!tableError) tableCount++;
    }
    
    console.log(`   Tables: ${tableCount}/${tables.length} accessible`);
    return tableCount === tables.length;

  } catch (error) {
    console.log('❌ Supabase error:', error.message);
    return false;
  }
}

async function testInfluxDB() {
  console.log('\n📈 Testing InfluxDB...');
  try {
    const influxDB = new InfluxDB({
      url: process.env.INFLUXDB_URL,
      token: process.env.INFLUXDB_TOKEN,
    });

    const writeApi = influxDB.getWriteApi(
      process.env.INFLUXDB_ORG,
      process.env.INFLUXDB_BUCKET,
      'ns'
    );

    // Test write
    const testPoint = new Point('connection_test')
      .tag('source', 'final-test')
      .floatField('value', 1.0)
      .timestamp(new Date());

    writeApi.writePoint(testPoint);
    await writeApi.flush();
    await writeApi.close();

    console.log('✅ InfluxDB connected successfully');
    console.log(`   URL: ${process.env.INFLUXDB_URL}`);
    console.log(`   Org: ${process.env.INFLUXDB_ORG}`);
    console.log(`   Bucket: ${process.env.INFLUXDB_BUCKET}`);
    return true;

  } catch (error) {
    console.log('❌ InfluxDB error:', error.message);
    return false;
  }
}

async function testBackend() {
  console.log('\n🔧 Testing Backend...');
  try {
    const response = await fetch('http://localhost:3001/health');
    if (!response.ok) {
      console.log('❌ Backend not responding');
      return false;
    }

    const data = await response.json();
    console.log('✅ Backend connected successfully');
    console.log(`   Status: ${data.status}`);
    console.log(`   Services: ${Object.keys(data.services).length} healthy`);
    return true;

  } catch (error) {
    console.log('❌ Backend error:', error.message);
    return false;
  }
}

async function runFinalTest() {
  console.log('🚀 MEV Arbitrage Bot - Final Connection Test\n');
  
  const results = {
    supabase: await testSupabase(),
    influxdb: await testInfluxDB(),
    backend: await testBackend()
  };

  console.log('\n' + '='.repeat(50));
  console.log('📋 FINAL CONNECTION TEST RESULTS');
  console.log('='.repeat(50));
  console.log(`Supabase:  ${results.supabase ? '✅ CONNECTED' : '❌ FAILED'}`);
  console.log(`InfluxDB:  ${results.influxdb ? '✅ CONNECTED' : '❌ FAILED'}`);
  console.log(`Backend:   ${results.backend ? '✅ CONNECTED' : '❌ FAILED'}`);
  
  const totalTests = Object.keys(results).length;
  const passedTests = Object.values(results).filter(Boolean).length;
  
  console.log(`\nOverall: ${passedTests}/${totalTests} systems connected`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 ALL SYSTEMS CONNECTED SUCCESSFULLY!');
    console.log('🚀 Your MEV Arbitrage Bot is ready for production!');
    console.log('\n📊 System Architecture:');
    console.log('   ✅ Supabase    → Persistent data storage');
    console.log('   ✅ InfluxDB    → Time-series analytics');
    console.log('   ✅ Backend     → Real-time processing');
    console.log('   ✅ Frontend    → User interface');
    console.log('\n🎯 Next Steps:');
    console.log('   • Start trading with real data');
    console.log('   • Monitor performance metrics');
    console.log('   • Scale to production networks');
  } else {
    console.log('\n⚠️  Some systems need attention.');
    console.log('📚 Check the error messages above for troubleshooting.');
  }
}

runFinalTest().catch(console.error);
