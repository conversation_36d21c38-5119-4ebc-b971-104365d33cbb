# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration
REDIS_URL=redis://localhost:6379

# Supabase Configuration
SUPABASE_URL=https://sbgeliidkalbnywvaiwe.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNiZ2VsaWlka2FsYm55d3ZhaXdlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg0OTgxMjUsImV4cCI6MjA2NDA3NDEyNX0.QxQSP8fRq4UNFA9VmkR_-cxp7H_TyYgGQwwofBsZRZc
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNiZ2VsaWlka2FsYm55d3ZhaXdlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ5ODEyNSwiZXhwIjoyMDY0MDc0MTI1fQ.rDIUb3-EwnOdylGB_HhlZYVr2D9VdW4dE9cWm-L3iQQ
SUPABASE_JWT_SECRET=GYNv5ieDwDAdAZljg/TCkzYfgle2Qiscgd6ihyDYyVT6XTPBm2YW23FxKTlA8CU7Ab+JgdKl5t47mCr7+u0JEw==
SUPABASE_DATABASE_URL="postgresql://postgres.sbgeliidkalbnywvaiwe:[YOUR-PASSWORD]@aws-0-us-east-2.pooler.supabase.com:5432/postgres"
# Connect to Supabase via connection pooling
SUPABASE_DATABASE_URL="postgresql://postgres.sbgeliidkalbnywvaiwe:[YOUR-PASSWORD]@aws-0-us-east-2.pooler.supabase.com:6543/postgres?pgbouncer=true"


# InfluxDB Configuration
INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=0yplGOzAMMkssXmfL_kD40Ey78LZ8_De2RbvK5Z-s-bIMZPbU_F2k-ZB3_jo-wpUbPZhsf3VDcgGwu3jwy5Ctw==
INFLUXDB_ORG=Dev-KE
INFLUXDB_BUCKET=mev-monitoring
INFLUXDB_USERNAME=Simm40
INFLUXDB_PASSWORD=40ayamkingofTyre!

# Blockchain Configuration (using local Hardhat network)
ETHEREUM_RPC_URL=http://127.0.0.1:8545
POLYGON_RPC_URL=http://127.0.0.1:8545
BSC_RPC_URL=http://127.0.0.1:8545
SOLANA_RPC_URL=http://127.0.0.1:8545

# Local Contract Addresses
TOKEN_DISCOVERY_ADDRESS=******************************************
LIQUIDITY_CHECKER_ADDRESS=******************************************
ARBITRAGE_EXECUTOR_ADDRESS=******************************************
GOVERNANCE_ADDRESS=******************************************

# Local Hardhat Account (for testing)
PRIVATE_KEY=0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80

# Trading Configuration
MIN_PROFIT_THRESHOLD=50
MAX_POSITION_SIZE=10000
MAX_SLIPPAGE=0.5
GAS_PRICE_MULTIPLIER=1.1

# Risk Management
EMERGENCY_STOP=false
MAX_DAILY_LOSS=1000
POSITION_SIZE_PERCENTAGE=2

# Monitoring
LOG_LEVEL=info
ENABLE_METRICS=true

# External API Keys (for production use)
COINGECKO_API_KEY=your-coingecko-api-key-here
ETHERSCAN_API_KEY=your-etherscan-api-key-here
POLYGONSCAN_API_KEY=your-polygonscan-api-key-here
BSCSCAN_API_KEY=your-bscscan-api-key-here
ALCHEMY_API_KEY=your-alchemy-api-key-here
INFURA_PROJECT_ID=your-infura-project-id-here
MORALIS_API_KEY=your-moralis-api-key-here

# Flashbots Configuration
FLASHBOTS_RELAY_URL=https://relay.flashbots.net
FLASHBOTS_BUILDER_URL=https://builder.flashbots.net

# Telegram Bot (for alerts)
TELEGRAM_BOT_TOKEN=your-telegram-bot-token-here
TELEGRAM_CHAT_ID=your-telegram-chat-id-here

# Email Notifications
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password-here
ALERT_EMAIL=<EMAIL>

# Security
JWT_SECRET=your-jwt-secret-for-api-authentication
API_RATE_LIMIT_WINDOW=900000
API_RATE_LIMIT_MAX=100
CORS_ORIGIN=http://localhost:5173,http://localhost:3000


