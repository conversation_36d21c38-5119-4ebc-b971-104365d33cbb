{"_format": "hh-sol-artifact-1", "contractName": "TokenDiscovery", "sourceName": "contracts/TokenDiscovery.sol", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "caller", "type": "address"}], "name": "AuthorizedCallerAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "caller", "type": "address"}], "name": "AuthorizedCallerRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": false, "internalType": "string", "name": "reason", "type": "string"}], "name": "TokenBlacklisted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}], "name": "TokenRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": false, "internalType": "string", "name": "name", "type": "string"}, {"indexed": false, "internalType": "string", "name": "symbol", "type": "string"}], "name": "TokenWhitelisted", "type": "event"}, {"inputs": [], "name": "MIN_LIQUIDITY_THRESHOLD", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MIN_SAFETY_SCORE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "caller", "type": "address"}], "name": "addAuthorizedCaller", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "string", "name": "reason", "type": "string"}], "name": "addToBlacklist", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint256", "name": "minLiquidity", "type": "uint256"}, {"internalType": "uint8", "name": "safetyScore", "type": "uint8"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "authorizedCallers", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "blacklistedTokens", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}], "name": "getTokenInfo", "outputs": [{"components": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint256", "name": "minLiquidity", "type": "uint256"}, {"internalType": "uint8", "name": "safetyScore", "type": "uint8"}, {"internalType": "bool", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bool"}, {"internalType": "bool", "name": "isBlacklisted", "type": "bool"}, {"internalType": "uint256", "name": "addedTimestamp", "type": "uint256"}], "internalType": "struct TokenDiscovery.TokenInfo", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>T<PERSON><PERSON>", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}], "name": "isTokenValid", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "caller", "type": "address"}], "name": "removeAuthorizedCaller", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "tokens", "outputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint256", "name": "minLiquidity", "type": "uint256"}, {"internalType": "uint8", "name": "safetyScore", "type": "uint8"}, {"internalType": "bool", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bool"}, {"internalType": "bool", "name": "isBlacklisted", "type": "bool"}, {"internalType": "uint256", "name": "addedTimestamp", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "whitelistedTokens", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}