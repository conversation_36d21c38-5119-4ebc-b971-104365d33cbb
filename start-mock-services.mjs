#!/usr/bin/env node

import { createServer } from 'http';
import { createServer as createHttpsServer } from 'https';
import { WebSocketServer } from 'ws';

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  blue: '\x1b[34m',
  yellow: '\x1b[33m',
  red: '\x1b[31m'
};

function log(message, color = 'reset') {
  const timestamp = new Date().toISOString();
  console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
}

// Mock Redis Server
class MockRedisServer {
  constructor(port = 6379) {
    this.port = port;
    this.data = new Map();
    this.server = null;
  }

  start() {
    return new Promise((resolve) => {
      this.server = createServer((req, res) => {
        // Simple HTTP interface for Redis-like operations
        if (req.method === 'GET' && req.url === '/ping') {
          res.writeHead(200, { 'Content-Type': 'text/plain' });
          res.end('PONG');
        } else if (req.method === 'POST' && req.url === '/set') {
          let body = '';
          req.on('data', chunk => body += chunk);
          req.on('end', () => {
            try {
              const { key, value, ttl } = JSON.parse(body);
              this.data.set(key, { value, expires: ttl ? Date.now() + (ttl * 1000) : null });
              res.writeHead(200, { 'Content-Type': 'application/json' });
              res.end(JSON.stringify({ success: true }));
            } catch (error) {
              res.writeHead(400, { 'Content-Type': 'application/json' });
              res.end(JSON.stringify({ error: error.message }));
            }
          });
        } else if (req.method === 'GET' && req.url.startsWith('/get/')) {
          const key = req.url.substring(5);
          const item = this.data.get(key);
          
          if (!item) {
            res.writeHead(404, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Key not found' }));
          } else if (item.expires && Date.now() > item.expires) {
            this.data.delete(key);
            res.writeHead(404, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Key expired' }));
          } else {
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ value: item.value }));
          }
        } else {
          res.writeHead(404, { 'Content-Type': 'text/plain' });
          res.end('Not Found');
        }
      });

      this.server.listen(this.port, () => {
        log(`Mock Redis server started on port ${this.port}`, 'green');
        resolve();
      });
    });
  }

  stop() {
    if (this.server) {
      this.server.close();
      log('Mock Redis server stopped', 'yellow');
    }
  }
}

// Mock InfluxDB Server
class MockInfluxDBServer {
  constructor(port = 8086) {
    this.port = port;
    this.data = [];
    this.server = null;
  }

  start() {
    return new Promise((resolve) => {
      this.server = createServer((req, res) => {
        // CORS headers
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

        if (req.method === 'OPTIONS') {
          res.writeHead(200);
          res.end();
          return;
        }

        if (req.method === 'GET' && req.url === '/health') {
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ status: 'pass', version: '2.0.0' }));
        } else if (req.method === 'POST' && req.url === '/api/v2/query') {
          // Mock query endpoint
          let body = '';
          req.on('data', chunk => body += chunk);
          req.on('end', () => {
            try {
              res.writeHead(200, { 'Content-Type': 'application/json' });
              res.end(JSON.stringify({ 
                results: [{ 
                  series: [{ 
                    name: 'buckets', 
                    columns: ['name'], 
                    values: [['mev-arbitrage-metrics']] 
                  }] 
                }] 
              }));
            } catch (error) {
              res.writeHead(400, { 'Content-Type': 'application/json' });
              res.end(JSON.stringify({ error: error.message }));
            }
          });
        } else if (req.method === 'POST' && req.url === '/api/v2/write') {
          // Mock write endpoint
          let body = '';
          req.on('data', chunk => body += chunk);
          req.on('end', () => {
            this.data.push({ timestamp: Date.now(), data: body });
            res.writeHead(204);
            res.end();
          });
        } else if (req.method === 'GET' && req.url.includes('/api/v2/query')) {
          // Mock query with flux
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ 
            results: [{ 
              series: [{ 
                name: 'test_measurement', 
                columns: ['time', 'value'], 
                values: [[new Date().toISOString(), 100]] 
              }] 
            }] 
          }));
        } else {
          res.writeHead(404, { 'Content-Type': 'text/plain' });
          res.end('Not Found');
        }
      });

      this.server.listen(this.port, () => {
        log(`Mock InfluxDB server started on port ${this.port}`, 'green');
        resolve();
      });
    });
  }

  stop() {
    if (this.server) {
      this.server.close();
      log('Mock InfluxDB server stopped', 'yellow');
    }
  }
}

// Mock Supabase Server
class MockSupabaseServer {
  constructor(port = 54321) {
    this.port = port;
    this.data = {
      opportunities: [],
      trades: [],
      validations: [],
      flash_loan_quotes: []
    };
    this.server = null;
  }

  start() {
    return new Promise((resolve) => {
      this.server = createServer((req, res) => {
        // CORS headers
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, apikey');

        if (req.method === 'OPTIONS') {
          res.writeHead(200);
          res.end();
          return;
        }

        const url = new URL(req.url, `http://localhost:${this.port}`);
        const pathParts = url.pathname.split('/').filter(Boolean);

        if (req.method === 'GET' && pathParts[0] === 'rest' && pathParts[1] === 'v1') {
          const table = pathParts[2];
          if (this.data[table]) {
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify(this.data[table]));
          } else {
            res.writeHead(404, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Table not found' }));
          }
        } else if (req.method === 'POST' && pathParts[0] === 'rest' && pathParts[1] === 'v1') {
          const table = pathParts[2];
          let body = '';
          req.on('data', chunk => body += chunk);
          req.on('end', () => {
            try {
              const data = JSON.parse(body);
              if (this.data[table]) {
                if (Array.isArray(data)) {
                  this.data[table].push(...data);
                } else {
                  this.data[table].push(data);
                }
                res.writeHead(201, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify(data));
              } else {
                res.writeHead(404, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'Table not found' }));
              }
            } catch (error) {
              res.writeHead(400, { 'Content-Type': 'application/json' });
              res.end(JSON.stringify({ error: error.message }));
            }
          });
        } else {
          res.writeHead(404, { 'Content-Type': 'text/plain' });
          res.end('Not Found');
        }
      });

      this.server.listen(this.port, () => {
        log(`Mock Supabase server started on port ${this.port}`, 'green');
        resolve();
      });
    });
  }

  stop() {
    if (this.server) {
      this.server.close();
      log('Mock Supabase server stopped', 'yellow');
    }
  }
}

// Main function to start all mock services
async function startMockServices() {
  console.log(`${colors.blue}Starting Mock Database Services for MEV Arbitrage Bot Testing${colors.reset}\n`);

  const redisServer = new MockRedisServer(6379);
  const influxServer = new MockInfluxDBServer(8086);
  const supabaseServer = new MockSupabaseServer(54321);

  try {
    await Promise.all([
      redisServer.start(),
      influxServer.start(),
      supabaseServer.start()
    ]);

    log('All mock services started successfully!', 'green');
    log('Services available:', 'blue');
    log('  • Redis (Mock): http://localhost:6379', 'blue');
    log('  • InfluxDB (Mock): http://localhost:8086', 'blue');
    log('  • Supabase (Mock): http://localhost:54321', 'blue');
    log('\nPress Ctrl+C to stop all services', 'yellow');

    // Handle graceful shutdown
    process.on('SIGINT', () => {
      log('\nShutting down mock services...', 'yellow');
      redisServer.stop();
      influxServer.stop();
      supabaseServer.stop();
      process.exit(0);
    });

    // Keep the process alive
    process.stdin.resume();

  } catch (error) {
    log(`Failed to start mock services: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Start the mock services
startMockServices();
