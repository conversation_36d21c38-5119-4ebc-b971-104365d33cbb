// Minimal server test
import express from 'express';
import cors from 'cors';
import config from './backend/config/index.js';
import logger from './backend/utils/logger.js';

console.log('Starting minimal server test...');

const app = express();

// Basic middleware
app.use(cors());
app.use(express.json());

// Health check route
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Start server
const port = parseInt(config.PORT);
const server = app.listen(port, () => {
  logger.info(`Minimal test server started on port ${port}`);
  console.log(`✓ Server running on http://localhost:${port}`);
  console.log('✓ Try: http://localhost:3001/health');
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('Shutting down...');
  server.close();
});

process.on('SIGINT', () => {
  console.log('Shutting down...');
  server.close();
});
