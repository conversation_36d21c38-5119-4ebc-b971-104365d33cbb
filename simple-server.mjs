#!/usr/bin/env node

import express from 'express';
import cors from 'cors';
import { createServer } from 'http';

const app = express();
const port = process.env.PORT || 8080;

// Middleware
app.use(cors());
app.use(express.json());

// Mock data for testing
const mockData = {
  opportunities: [
    {
      id: 'mock_opp_1',
      type: 'cross_exchange',
      assets: ['ETH', 'USDC'],
      exchanges: ['uniswap_v3', 'sushiswap'],
      potential_profit: 125.50,
      profit_percentage: 2.1,
      network: 'ethereum',
      confidence: 0.92,
      timestamp: new Date().toISOString()
    },
    {
      id: 'mock_opp_2',
      type: 'cross_chain',
      assets: ['USDC', 'USDC.e'],
      source_chain: 'ethereum',
      target_chain: 'polygon',
      potential_profit: 85.25,
      profit_percentage: 1.8,
      confidence: 0.87,
      timestamp: new Date().toISOString()
    }
  ],
  trades: [
    {
      id: 'mock_trade_1',
      opportunity_id: 'mock_opp_1',
      status: 'completed',
      actual_profit: 120.30,
      execution_time: 2.5,
      gas_used: 180000,
      timestamp: new Date(Date.now() - 300000).toISOString()
    }
  ],
  strategies: [
    {
      id: 'cross_exchange_v1',
      name: 'Cross Exchange Arbitrage',
      success_rate: 0.85,
      avg_profit: 95.50,
      total_trades: 150,
      status: 'active'
    }
  ],
  learningEvents: [
    {
      id: 'learning_1',
      type: 'strategy_optimization',
      description: 'Improved cross-exchange detection algorithm',
      impact: 'positive',
      timestamp: new Date(Date.now() - 600000).toISOString()
    }
  ]
};

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    success: true,
    status: 'healthy',
    timestamp: Date.now(),
    uptime: process.uptime(),
    data: {
      status: 'healthy',
      score: 85,
      services: {
        'api': true,
        'cache': true,
        'database': false,
        'websocket': true
      },
      databases: {
        'redis': { isHealthy: false, latency: 0 },
        'influxdb': { isHealthy: false, latency: 0 },
        'supabase': { isHealthy: true, latency: 25 },
        'postgres': { isHealthy: false, latency: 0 }
      },
      performance: {
        uptime: 85,
        issues: ['Database connections not available']
      }
    }
  });
});

// System metrics endpoint
app.get('/api/system/metrics', (req, res) => {
  res.json({
    success: true,
    data: {
      performance: {
        averageLatency: 45,
        systemUptime: 99.2,
        timestamp: Date.now()
      },
      cache: {
        hitRatio: 82.5,
        memoryUsage: 125,
        totalRequests: 1250
      },
      database: {
        redis: { isHealthy: false, latency: 0 },
        influxdb: { isHealthy: false, latency: 0 }
      },
      websocket: {
        activeConnections: 0,
        messagesSent: 0,
        errors: 0
      }
    },
    timestamp: Date.now()
  });
});

// System integration endpoint
app.get('/api/system/integration', (req, res) => {
  res.json({
    success: true,
    data: {
      isInitialized: true,
      totalServices: 25,
      enabledFeatures: {
        preExecutionValidation: true,
        mevProtection: true,
        flashLoans: true,
        profitValidation: true,
        enhancedTokenMonitoring: true,
        executionQueue: true,
        mlLearning: true,
        riskManagement: true,
        enhancedDataManagement: true,
        performanceMonitoring: true,
        webSocketService: true
      }
    },
    timestamp: Date.now()
  });
});

// Opportunities endpoint
app.get('/api/opportunities', (req, res) => {
  const { limit = 20, offset = 0 } = req.query;
  const start = parseInt(offset);
  const end = start + parseInt(limit);
  
  res.json({
    success: true,
    data: mockData.opportunities.slice(start, end),
    pagination: {
      limit: parseInt(limit),
      offset: parseInt(offset),
      total: mockData.opportunities.length
    },
    timestamp: Date.now()
  });
});

// Trades endpoint
app.get('/api/trades', (req, res) => {
  const { limit = 20, offset = 0 } = req.query;
  const start = parseInt(offset);
  const end = start + parseInt(limit);
  
  res.json({
    success: true,
    data: mockData.trades.slice(start, end),
    pagination: {
      limit: parseInt(limit),
      offset: parseInt(offset),
      total: mockData.trades.length
    },
    timestamp: Date.now()
  });
});

// Analytics endpoint
app.get('/api/analytics/performance', (req, res) => {
  res.json({
    success: true,
    data: {
      netProfit: 2450.75,
      winRate: 85.2,
      totalTrades: 150,
      dailyVolume: 125000
    },
    timestamp: Date.now()
  });
});

// ML endpoints
app.get('/api/ml/strategy-performance', (req, res) => {
  res.json({
    success: true,
    data: mockData.strategies,
    timestamp: Date.now()
  });
});

app.get('/api/ml/learning-events', (req, res) => {
  const { limit = 20 } = req.query;
  res.json({
    success: true,
    data: mockData.learningEvents.slice(0, parseInt(limit)),
    timestamp: Date.now()
  });
});

app.get('/api/ml/learning-stats', (req, res) => {
  res.json({
    success: true,
    data: {
      current_market_regime: 'Normal',
      total_strategies: 5,
      adaptation_rate: 2.3
    },
    timestamp: Date.now()
  });
});

// Queue status endpoint
app.get('/api/queue/status', (req, res) => {
  res.json({
    success: true,
    data: {
      length: 3,
      items: [
        { id: 'queue_item_1', priority: 0.95, status: 'pending' },
        { id: 'queue_item_2', priority: 0.87, status: 'pending' },
        { id: 'queue_item_3', priority: 0.82, status: 'pending' }
      ]
    },
    timestamp: Date.now()
  });
});

// System alerts endpoint
app.get('/api/system/alerts', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: 'alert_1',
        type: 'database',
        severity: 'medium',
        message: 'Database connections not available',
        timestamp: Date.now() - 300000,
        resolved: false
      }
    ],
    count: 1,
    timestamp: Date.now()
  });
});

// Cache flush endpoint
app.post('/api/cache/flush', (req, res) => {
  res.json({
    success: true,
    message: 'Cache flushed successfully (mock)',
    timestamp: Date.now()
  });
});

// Error handling
app.use((error, req, res, next) => {
  console.error('Error:', error);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    timestamp: Date.now()
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found',
    path: req.originalUrl,
    timestamp: Date.now()
  });
});

// Create HTTP server
const server = createServer(app);

// Start server
server.listen(port, () => {
  console.log(`✅ MEV Arbitrage Bot Server running on port ${port}`);
  console.log(`🌐 Health check: http://localhost:${port}/health`);
  console.log(`📊 System metrics: http://localhost:${port}/api/system/metrics`);
  console.log(`🎯 All endpoints available for testing`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 Received SIGTERM, shutting down gracefully...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT, shutting down gracefully...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});
