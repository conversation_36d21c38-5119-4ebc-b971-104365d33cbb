// Simplified backend server for testing
import express from 'express';
import cors from 'cors';
import { createServer } from 'http';
import { WebSocketServer } from 'ws';
import config from './backend/config/index.js';
import logger from './backend/utils/logger.js';

console.log('Starting MEV Arbitrage Bot backend...');

const app = express();
const server = createServer(app);
const wss = new WebSocketServer({ server });

// Middleware
app.use(cors());
app.use(express.json());

// Basic routes
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    services: {
      backend: true,
      websocket: true
    }
  });
});

// Mock API routes
app.get('/api/opportunities', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: 'opp_1',
        type: 'intra-chain',
        assets: ['ETH', 'USDC'],
        exchanges: ['Uniswap', 'SushiSwap'],
        potentialProfit: 125.50,
        profitPercentage: 2.1,
        timestamp: Date.now(),
        network: 'ethereum',
        confidence: 85
      }
    ],
    count: 1
  });
});

app.get('/api/trades', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: 'trade_1',
        type: 'intra-chain',
        assets: ['ETH', 'USDC'],
        exchanges: ['Uniswap', 'SushiSwap'],
        executedProfit: 118.25,
        gasFees: 7.25,
        status: 'success',
        timestamp: Date.now() - 300000,
        network: 'ethereum',
        txHash: '0x1234567890abcdef'
      }
    ],
    count: 1
  });
});

app.get('/api/tokens', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: 'ethereum_******************************************',
        name: 'Ethereum',
        symbol: 'ETH',
        address: '******************************************',
        liquidity: 1000000,
        safetyScore: 100,
        isWhitelisted: true,
        network: 'ethereum'
      }
    ],
    count: 1
  });
});

app.get('/api/analytics/performance', (req, res) => {
  res.json({
    success: true,
    data: {
      totalTrades: 45,
      successfulTrades: 39,
      totalProfit: 2850.75,
      netProfit: 2650.50,
      winRate: 86.7,
      avgProfit: 67.96,
      dailyVolume: 125000
    }
  });
});

// WebSocket handling
wss.on('connection', (ws) => {
  console.log('New WebSocket connection');
  
  ws.send(JSON.stringify({
    type: 'welcome',
    data: {
      message: 'Connected to MEV Arbitrage Bot',
      timestamp: new Date().toISOString()
    }
  }));
  
  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message.toString());
      console.log('WebSocket message:', data);
      
      if (data.type === 'subscribe') {
        ws.send(JSON.stringify({
          type: 'subscribed',
          channel: data.channel,
          timestamp: new Date().toISOString()
        }));
      }
    } catch (error) {
      console.error('WebSocket message error:', error);
    }
  });
});

// Start server
const port = parseInt(config.PORT);
server.listen(port, () => {
  logger.info(`MEV Arbitrage Bot backend started on port ${port}`);
  console.log(`✓ Backend server running on http://localhost:${port}`);
  console.log(`✓ WebSocket server ready`);
  console.log(`✓ Health check: http://localhost:${port}/health`);
  console.log(`✓ API endpoints: http://localhost:${port}/api/*`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('Shutting down backend...');
  server.close();
});

process.on('SIGINT', () => {
  console.log('Shutting down backend...');
  server.close();
});
