{"_format": "hh-sol-cache-2", "files": {"C:\\DevEnv\\Blockchain\\Syst\\mev-arbitrage-bot\\contracts\\ArbitrageExecutor.sol": {"lastModificationDate": 1748436205620, "contentHash": "7dbca1cd7eda896a48facc6cd5cdb244", "sourceName": "contracts/ArbitrageExecutor.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/access/Ownable.sol", "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "@openzeppelin/contracts/utils/Pausable.sol", "./interfaces/IERC20.sol", "./interfaces/IFlashLoanReceiver.sol", "./TokenDiscovery.sol", "./LiquidityChecker.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ArbitrageExecutor"]}, "C:\\DevEnv\\Blockchain\\Syst\\mev-arbitrage-bot\\contracts\\interfaces\\IERC20.sol": {"lastModificationDate": 1748436257231, "contentHash": "********************************", "sourceName": "contracts/interfaces/IERC20.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC20"]}, "C:\\DevEnv\\Blockchain\\Syst\\mev-arbitrage-bot\\contracts\\interfaces\\IFlashLoanReceiver.sol": {"lastModificationDate": 1748436273907, "contentHash": "b8d1728470201c93a22cca8bc4c7360d", "sourceName": "contracts/interfaces/IFlashLoanReceiver.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IFlashLoanReceiver", "IUniswapV2Pair", "IUniswapV2Router"]}, "C:\\DevEnv\\Blockchain\\Syst\\mev-arbitrage-bot\\contracts\\TokenDiscovery.sol": {"lastModificationDate": 1748436227699, "contentHash": "c9cc03ac4398b8609c69e57a31bb0552", "sourceName": "contracts/TokenDiscovery.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/access/Ownable.sol", "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "./interfaces/IERC20.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["TokenDiscovery"]}, "C:\\DevEnv\\Blockchain\\Syst\\mev-arbitrage-bot\\contracts\\LiquidityChecker.sol": {"lastModificationDate": 1748436303154, "contentHash": "0e9b31ff92f4863d5dc976ab9161c99f", "sourceName": "contracts/LiquidityChecker.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./interfaces/IFlashLoanReceiver.sol", "./interfaces/IERC20.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["LiquidityChecker"]}, "C:\\DevEnv\\Blockchain\\Syst\\mev-arbitrage-bot\\node_modules\\@openzeppelin\\contracts\\utils\\ReentrancyGuard.sol": {"lastModificationDate": 1748430373295, "contentHash": "190613e556d509d9e9a0ea43dc5d891d", "sourceName": "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Reentrancy<PERSON><PERSON>"]}, "C:\\DevEnv\\Blockchain\\Syst\\mev-arbitrage-bot\\node_modules\\@openzeppelin\\contracts\\utils\\Pausable.sol": {"lastModificationDate": 1748430373159, "contentHash": "0d47b53e10b1985efbb396f937626279", "sourceName": "@openzeppelin/contracts/utils/Pausable.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../utils/Context.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Pausable"]}, "C:\\DevEnv\\Blockchain\\Syst\\mev-arbitrage-bot\\node_modules\\@openzeppelin\\contracts\\access\\Ownable.sol": {"lastModificationDate": 1748430372650, "contentHash": "d3c790edc9ccf808a17c5a6cd13614fd", "sourceName": "@openzeppelin/contracts/access/Ownable.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../utils/Context.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Ownable"]}, "C:\\DevEnv\\Blockchain\\Syst\\mev-arbitrage-bot\\node_modules\\@openzeppelin\\contracts\\utils\\Context.sol": {"lastModificationDate": 1748430360611, "contentHash": "67bfbc07588eb8683b3fd8f6f909563e", "sourceName": "@openzeppelin/contracts/utils/Context.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Context"]}, "C:\\DevEnv\\Blockchain\\Syst\\mev-arbitrage-bot\\contracts\\Governance.sol": {"lastModificationDate": 1748436217082, "contentHash": "9582ca2915022aa237591156a555b41b", "sourceName": "contracts/Governance.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/access/Ownable.sol", "@openzeppelin/contracts/utils/ReentrancyGuard.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Governance"]}}}