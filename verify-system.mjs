#!/usr/bin/env node

/**
 * MEV Arbitrage Bot - System Verification Script
 * ==============================================
 * 
 * This script verifies:
 * 1. All database connections (Redis, PostgreSQL, InfluxDB, Supabase)
 * 2. Backend API endpoints
 * 3. Data routing and storage
 * 4. Frontend accessibility
 * 5. Real-time data flow
 */

import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';
import path from 'path';

const execAsync = promisify(exec);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = (message, color = colors.reset) => {
  console.log(`${color}${message}${colors.reset}`);
};

const logSuccess = (message) => {
  log(`${colors.green}✅ ${message}${colors.reset}`);
};

const logError = (message) => {
  log(`${colors.red}❌ ${message}${colors.reset}`);
};

const logWarning = (message) => {
  log(`${colors.yellow}⚠️  ${message}${colors.reset}`);
};

const logInfo = (message) => {
  log(`${colors.blue}ℹ️  ${message}${colors.reset}`);
};

const logStep = (step, message) => {
  log(`\n${colors.bright}[STEP ${step}]${colors.reset} ${colors.cyan}${message}${colors.reset}`);
};

// Test results tracking
const testResults = {
  databases: {},
  backend: {},
  frontend: {},
  dataFlow: {},
  overall: { passed: 0, failed: 0, warnings: 0 }
};

// Helper function to test HTTP endpoints
async function testEndpoint(url, expectedStatus = 200) {
  try {
    const response = await fetch(url);
    return {
      success: response.status === expectedStatus,
      status: response.status,
      data: response.ok ? await response.json() : null
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

// Test Docker services
async function testDockerServices() {
  logStep(1, 'Testing Docker Services');
  
  const services = ['redis', 'postgres', 'influxdb'];
  
  for (const service of services) {
    try {
      const { stdout } = await execAsync(`docker-compose ps ${service}`);
      const isRunning = stdout.includes('Up') || stdout.includes('running');
      
      if (isRunning) {
        logSuccess(`${service} container is running`);
        testResults.databases[service] = true;
        testResults.overall.passed++;
      } else {
        logError(`${service} container is not running`);
        testResults.databases[service] = false;
        testResults.overall.failed++;
      }
    } catch (error) {
      logError(`Failed to check ${service}: ${error.message}`);
      testResults.databases[service] = false;
      testResults.overall.failed++;
    }
  }
}

// Test database connections
async function testDatabaseConnections() {
  logStep(2, 'Testing Database Connections');
  
  // Test Redis
  try {
    await execAsync('redis-cli ping');
    logSuccess('Redis connection successful');
    testResults.databases.redisConnection = true;
    testResults.overall.passed++;
  } catch (error) {
    logError('Redis connection failed');
    testResults.databases.redisConnection = false;
    testResults.overall.failed++;
  }
  
  // Test PostgreSQL
  try {
    const testCommand = 'docker exec mev-postgres pg_isready -U mev_user -d mev_arbitrage_bot';
    await execAsync(testCommand);
    logSuccess('PostgreSQL connection successful');
    testResults.databases.postgresConnection = true;
    testResults.overall.passed++;
  } catch (error) {
    logError('PostgreSQL connection failed');
    testResults.databases.postgresConnection = false;
    testResults.overall.failed++;
  }
  
  // Test InfluxDB
  const influxTest = await testEndpoint('http://localhost:8086/health');
  if (influxTest.success) {
    logSuccess('InfluxDB connection successful');
    testResults.databases.influxConnection = true;
    testResults.overall.passed++;
  } else {
    logError('InfluxDB connection failed');
    testResults.databases.influxConnection = false;
    testResults.overall.failed++;
  }
}

// Test backend API endpoints
async function testBackendAPI() {
  logStep(3, 'Testing Backend API Endpoints');
  
  const endpoints = [
    { url: 'http://localhost:8080/health', name: 'Health Check' },
    { url: 'http://localhost:8080/api/opportunities', name: 'Opportunities' },
    { url: 'http://localhost:8080/api/trades', name: 'Trades' },
    { url: 'http://localhost:8080/api/tokens', name: 'Tokens' },
    { url: 'http://localhost:8080/api/analytics/performance', name: 'Analytics' },
    { url: 'http://localhost:8080/api/system/health', name: 'System Health' },
    { url: 'http://localhost:8080/api/realtime/update', name: 'Real-time Updates' }
  ];
  
  for (const endpoint of endpoints) {
    const result = await testEndpoint(endpoint.url);
    
    if (result.success) {
      logSuccess(`${endpoint.name} endpoint working`);
      testResults.backend[endpoint.name.toLowerCase().replace(/\s+/g, '')] = true;
      testResults.overall.passed++;
      
      // Log additional info for health endpoint
      if (endpoint.name === 'Health Check' && result.data) {
        logInfo(`Backend version: ${result.data.version || 'unknown'}`);
        if (result.data.databases) {
          logInfo(`Database status: Supabase=${result.data.databases.supabase}, InfluxDB=${result.data.databases.influxdb}, Redis=${result.data.databases.redis}`);
        }
      }
    } else {
      logError(`${endpoint.name} endpoint failed: ${result.error || result.status}`);
      testResults.backend[endpoint.name.toLowerCase().replace(/\s+/g, '')] = false;
      testResults.overall.failed++;
    }
  }
}

// Test frontend accessibility
async function testFrontend() {
  logStep(4, 'Testing Frontend Accessibility');
  
  const frontendPath = path.resolve('index.html');
  
  if (fs.existsSync(frontendPath)) {
    logSuccess('Frontend file exists');
    testResults.frontend.fileExists = true;
    testResults.overall.passed++;
    
    // Check if frontend can be opened
    const frontendUrl = `file://${frontendPath}`;
    logInfo(`Frontend URL: ${frontendUrl}`);
    
    // Read frontend content to verify it's valid
    try {
      const content = fs.readFileSync(frontendPath, 'utf8');
      if (content.includes('MEV Arbitrage Bot') && content.includes('dashboard')) {
        logSuccess('Frontend content is valid');
        testResults.frontend.contentValid = true;
        testResults.overall.passed++;
      } else {
        logWarning('Frontend content may be incomplete');
        testResults.frontend.contentValid = false;
        testResults.overall.warnings++;
      }
    } catch (error) {
      logError('Failed to read frontend content');
      testResults.frontend.contentValid = false;
      testResults.overall.failed++;
    }
  } else {
    logError('Frontend file not found');
    testResults.frontend.fileExists = false;
    testResults.overall.failed++;
  }
}

// Test data flow and routing
async function testDataFlow() {
  logStep(5, 'Testing Data Flow and Routing');
  
  // Test if backend can fetch and return data
  const opportunitiesTest = await testEndpoint('http://localhost:8080/api/opportunities');
  if (opportunitiesTest.success && opportunitiesTest.data && opportunitiesTest.data.data) {
    const opportunities = opportunitiesTest.data.data;
    if (opportunities.length > 0) {
      logSuccess(`Data flow working - ${opportunities.length} opportunities retrieved`);
      logInfo(`Data source: ${opportunitiesTest.data.source || 'unknown'}`);
      testResults.dataFlow.opportunities = true;
      testResults.overall.passed++;
    } else {
      logWarning('No opportunities data available');
      testResults.dataFlow.opportunities = false;
      testResults.overall.warnings++;
    }
  } else {
    logError('Failed to retrieve opportunities data');
    testResults.dataFlow.opportunities = false;
    testResults.overall.failed++;
  }
  
  // Test trades data
  const tradesTest = await testEndpoint('http://localhost:8080/api/trades');
  if (tradesTest.success && tradesTest.data && tradesTest.data.data) {
    const trades = tradesTest.data.data;
    if (trades.length > 0) {
      logSuccess(`Trades data flow working - ${trades.length} trades retrieved`);
      testResults.dataFlow.trades = true;
      testResults.overall.passed++;
    } else {
      logWarning('No trades data available');
      testResults.dataFlow.trades = false;
      testResults.overall.warnings++;
    }
  } else {
    logError('Failed to retrieve trades data');
    testResults.dataFlow.trades = false;
    testResults.overall.failed++;
  }
  
  // Test analytics data
  const analyticsTest = await testEndpoint('http://localhost:8080/api/analytics/performance');
  if (analyticsTest.success && analyticsTest.data && analyticsTest.data.data) {
    const metrics = analyticsTest.data.data;
    if (metrics.totalTrades !== undefined) {
      logSuccess('Analytics data flow working');
      logInfo(`Performance metrics: ${metrics.totalTrades} trades, ${metrics.winRate}% win rate`);
      testResults.dataFlow.analytics = true;
      testResults.overall.passed++;
    } else {
      logWarning('Analytics data incomplete');
      testResults.dataFlow.analytics = false;
      testResults.overall.warnings++;
    }
  } else {
    logError('Failed to retrieve analytics data');
    testResults.dataFlow.analytics = false;
    testResults.overall.failed++;
  }
}

// Test real-time functionality
async function testRealTimeFeatures() {
  logStep(6, 'Testing Real-time Features');
  
  // Test real-time updates endpoint
  const realtimeTest = await testEndpoint('http://localhost:8080/api/realtime/update');
  if (realtimeTest.success && realtimeTest.data) {
    logSuccess('Real-time updates endpoint working');
    logInfo(`Latest update: ${realtimeTest.data.data.timestamp}`);
    testResults.dataFlow.realtime = true;
    testResults.overall.passed++;
  } else {
    logError('Real-time updates failed');
    testResults.dataFlow.realtime = false;
    testResults.overall.failed++;
  }
  
  // Test multiple requests to simulate real-time behavior
  logInfo('Testing multiple real-time requests...');
  let successCount = 0;
  for (let i = 0; i < 3; i++) {
    const test = await testEndpoint('http://localhost:8080/api/realtime/update');
    if (test.success) successCount++;
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  if (successCount === 3) {
    logSuccess('Real-time consistency test passed');
    testResults.dataFlow.realtimeConsistency = true;
    testResults.overall.passed++;
  } else {
    logWarning(`Real-time consistency test partial: ${successCount}/3 requests succeeded`);
    testResults.dataFlow.realtimeConsistency = false;
    testResults.overall.warnings++;
  }
}

// Generate comprehensive report
function generateReport() {
  log('\n' + colors.bright + '📊 COMPREHENSIVE SYSTEM VERIFICATION REPORT' + colors.reset);
  log('=' .repeat(60));
  
  // Summary
  const total = testResults.overall.passed + testResults.overall.failed + testResults.overall.warnings;
  const successRate = total > 0 ? ((testResults.overall.passed / total) * 100).toFixed(1) : 0;
  
  log(`\n${colors.cyan}📈 SUMMARY${colors.reset}`);
  log(`   Total Tests: ${total}`);
  log(`   Passed: ${colors.green}${testResults.overall.passed}${colors.reset}`);
  log(`   Failed: ${colors.red}${testResults.overall.failed}${colors.reset}`);
  log(`   Warnings: ${colors.yellow}${testResults.overall.warnings}${colors.reset}`);
  log(`   Success Rate: ${successRate >= 80 ? colors.green : successRate >= 60 ? colors.yellow : colors.red}${successRate}%${colors.reset}`);
  
  // Database status
  log(`\n${colors.cyan}🗄️ DATABASE STATUS${colors.reset}`);
  Object.entries(testResults.databases).forEach(([key, value]) => {
    const status = value ? `${colors.green}✅ PASS${colors.reset}` : `${colors.red}❌ FAIL${colors.reset}`;
    log(`   ${key}: ${status}`);
  });
  
  // Backend status
  log(`\n${colors.cyan}📡 BACKEND API STATUS${colors.reset}`);
  Object.entries(testResults.backend).forEach(([key, value]) => {
    const status = value ? `${colors.green}✅ PASS${colors.reset}` : `${colors.red}❌ FAIL${colors.reset}`;
    log(`   ${key}: ${status}`);
  });
  
  // Frontend status
  log(`\n${colors.cyan}🌐 FRONTEND STATUS${colors.reset}`);
  Object.entries(testResults.frontend).forEach(([key, value]) => {
    const status = value ? `${colors.green}✅ PASS${colors.reset}` : `${colors.red}❌ FAIL${colors.reset}`;
    log(`   ${key}: ${status}`);
  });
  
  // Data flow status
  log(`\n${colors.cyan}🔄 DATA FLOW STATUS${colors.reset}`);
  Object.entries(testResults.dataFlow).forEach(([key, value]) => {
    const status = value ? `${colors.green}✅ PASS${colors.reset}` : `${colors.red}❌ FAIL${colors.reset}`;
    log(`   ${key}: ${status}`);
  });
  
  // Recommendations
  log(`\n${colors.cyan}💡 RECOMMENDATIONS${colors.reset}`);
  
  if (testResults.overall.failed > 0) {
    log(`   ${colors.red}• Fix failed components before production use${colors.reset}`);
  }
  
  if (testResults.overall.warnings > 0) {
    log(`   ${colors.yellow}• Review warnings for potential improvements${colors.reset}`);
  }
  
  if (!testResults.databases.supabase) {
    log(`   ${colors.yellow}• Consider setting up Supabase for persistent storage${colors.reset}`);
  }
  
  if (successRate >= 90) {
    log(`   ${colors.green}• System is ready for production deployment${colors.reset}`);
  } else if (successRate >= 70) {
    log(`   ${colors.yellow}• System is functional but needs improvements${colors.reset}`);
  } else {
    log(`   ${colors.red}• System requires significant fixes before use${colors.reset}`);
  }
  
  log('\n' + '=' .repeat(60));
  
  if (successRate >= 80) {
    log(`${colors.green}🎉 SYSTEM VERIFICATION COMPLETED SUCCESSFULLY!${colors.reset}\n`);
  } else {
    log(`${colors.yellow}⚠️  SYSTEM VERIFICATION COMPLETED WITH ISSUES${colors.reset}\n`);
  }
}

// Main verification function
async function runVerification() {
  try {
    log(`${colors.bright}🔍 MEV ARBITRAGE BOT - SYSTEM VERIFICATION${colors.reset}`);
    log('=' .repeat(50));
    
    await testDockerServices();
    await testDatabaseConnections();
    await testBackendAPI();
    await testFrontend();
    await testDataFlow();
    await testRealTimeFeatures();
    
    generateReport();
    
  } catch (error) {
    logError(`Verification failed: ${error.message}`);
    process.exit(1);
  }
}

// Run the verification
runVerification();
