#!/usr/bin/env node

console.log('Starting debug startup script...');

// Load environment variables from .env file
async function loadEnvFile() {
  try {
    const { promises: fs } = await import('fs');
    const path = await import('path');
    const { fileURLToPath } = await import('url');
    
    const envPath = path.join(path.dirname(fileURLToPath(import.meta.url)), '.env');
    console.log('Loading .env from:', envPath);
    
    const envContent = await fs.readFile(envPath, 'utf8');
    console.log('Loaded .env file, length:', envContent.length);
    
    envContent.split('\n').forEach(line => {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [key, ...valueParts] = trimmedLine.split('=');
        if (key && valueParts.length > 0) {
          const value = valueParts.join('=').trim();
          if (!process.env[key]) {
            process.env[key] = value;
          }
        }
      }
    });
    
    console.log('Environment variables loaded successfully');
  } catch (error) {
    console.log('Error loading .env file:', error.message);
  }
}

console.log('About to load environment...');
await loadEnvFile();

console.log('Environment loaded, checking variables...');
console.log('REDIS_URL:', process.env.REDIS_URL ? 'SET' : 'NOT SET');
console.log('INFLUXDB_URL:', process.env.INFLUXDB_URL ? 'SET' : 'NOT SET');
console.log('SUPABASE_URL:', process.env.SUPABASE_URL ? 'SET' : 'NOT SET');

console.log('Testing basic server startup...');

const { spawn } = await import('child_process');

const server = spawn('node', ['basic-server.mjs'], {
  stdio: 'pipe',
  env: {
    ...process.env,
    PORT: '8080'
  }
});

server.stdout.on('data', (data) => {
  console.log('SERVER OUTPUT:', data.toString());
});

server.stderr.on('data', (data) => {
  console.log('SERVER ERROR:', data.toString());
});

server.on('close', (code) => {
  console.log('Server exited with code:', code);
});

console.log('Server spawn initiated, waiting for startup...');

// Wait a bit for server to start
setTimeout(async () => {
  console.log('Testing server health...');
  
  try {
    const response = await fetch('http://localhost:8080/health');
    const data = await response.json();
    console.log('Health check successful:', data.success);
    console.log('Server is working!');
    
    // Kill server
    server.kill();
    process.exit(0);
  } catch (error) {
    console.log('Health check failed:', error.message);
    server.kill();
    process.exit(1);
  }
}, 3000);

console.log('Debug startup script completed setup');
