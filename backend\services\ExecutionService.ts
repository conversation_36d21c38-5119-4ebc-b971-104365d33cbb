import { EventEmitter } from 'events';
import { ethers } from 'ethers';
import logger from '../utils/logger.js';
import config from '../config/index.js';
import { ArbitrageOpportunity, ArbitrageType } from './OpportunityDetectionService.js';
import { MLLearningService } from './MLLearningService.js';
import { StrategySelectionService } from './StrategySelectionService.js';
import { MEVProtectionService, MEVProtectionResult } from './MEVProtectionService.js';
import { FlashLoanService, FlashLoanExecution, FlashLoanOptimization } from './FlashLoanService.js';
import { ProfitValidationService, ProfitValidationResult } from './ProfitValidationService.js';
import { ProfitPrioritizedExecutionQueue, QueuedOpportunity } from './ProfitPrioritizedExecutionQueue.js';

export enum TradeStatus {
  PENDING = 'pending',
  EXECUTING = 'executing',
  SUCCESS = 'success',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export interface Trade {
  id: string;
  opportunityId: string;
  type: ArbitrageType;
  assets: string[];
  exchanges: string[];
  executedProfit: number;
  gasFees: number;
  status: TradeStatus;
  timestamp: number;
  network: string;
  txHash?: string;
  errorMessage?: string;
  executionTime?: number;
  mevProtectionResult?: MEVProtectionResult; // Added for MEV protection tracking
  flashLoanExecution?: FlashLoanExecution; // Added for flash loan tracking
  profitValidationResult?: ProfitValidationResult; // Added for profit validation tracking
}

export interface ExecutionResult {
  success: boolean;
  trade?: Trade;
  error?: string;
  txHash?: string;
}

export class ExecutionService extends EventEmitter {
  private providers: Map<string, ethers.JsonRpcProvider> = new Map();
  private wallets: Map<string, ethers.Wallet> = new Map();
  private activeTrades: Map<string, Trade> = new Map();
  private isRunning = false;
  private isPaused = false;
  private executionInterval: NodeJS.Timeout | null = null;

  // Execution parameters
  private maxConcurrentTrades = 3;
  private gasMultiplier = parseFloat(config.GAS_PRICE_MULTIPLIER) / 100;
  private maxSlippage = parseFloat(config.MAX_SLIPPAGE);

  // ML Integration
  private mlLearningService: MLLearningService | null = null;
  private strategySelectionService: StrategySelectionService | null = null;

  // MEV Protection Integration
  private mevProtectionService: MEVProtectionService | null = null;

  // Flash Loan Integration
  private flashLoanService: FlashLoanService | null = null;

  // Profit Validation Integration
  private profitValidationService: ProfitValidationService | null = null;

  // Enhanced Execution Queue
  private executionQueue: ProfitPrioritizedExecutionQueue | null = null;

  constructor() {
    super();
    this.initializeProviders();
    this.initializeWallets();
    this.initializeExecutionQueue();
  }

  public setMLServices(mlLearningService: MLLearningService, strategySelectionService: StrategySelectionService) {
    this.mlLearningService = mlLearningService;
    this.strategySelectionService = strategySelectionService;
    logger.info('ML services integrated with ExecutionService');
  }

  public setMEVProtectionService(mevProtectionService: MEVProtectionService) {
    this.mevProtectionService = mevProtectionService;
    logger.info('MEV Protection Service integrated with ExecutionService');
  }

  public setFlashLoanService(flashLoanService: FlashLoanService) {
    this.flashLoanService = flashLoanService;
    logger.info('Flash Loan Service integrated with ExecutionService');
  }

  public setProfitValidationService(profitValidationService: ProfitValidationService) {
    this.profitValidationService = profitValidationService;
    logger.info('Profit Validation Service integrated with ExecutionService');
  }

  private initializeExecutionQueue() {
    this.executionQueue = new ProfitPrioritizedExecutionQueue();
    logger.info('Profit Prioritized Execution Queue initialized');
  }

  private initializeProviders() {
    this.providers.set('ethereum', new ethers.JsonRpcProvider(config.ETHEREUM_RPC_URL));
    this.providers.set('polygon', new ethers.JsonRpcProvider(config.POLYGON_RPC_URL));
    this.providers.set('bsc', new ethers.JsonRpcProvider(config.BSC_RPC_URL));
  }

  private initializeWallets() {
    if (config.PRIVATE_KEY) {
      try {
        this.providers.forEach((provider, network) => {
          const wallet = new ethers.Wallet(config.PRIVATE_KEY!, provider);
          this.wallets.set(network, wallet);
        });
        logger.info('Wallets initialized for trading');
      } catch (error) {
        logger.error('Failed to initialize wallets:', error);
      }
    } else {
      logger.warn('No private key configured - trading will be simulated');
    }
  }

  public async start() {
    if (this.isRunning) return;

    logger.info('Starting Execution Service...');
    this.isRunning = true;
    this.isPaused = false;

    // Start execution queue
    if (this.executionQueue) {
      await this.executionQueue.start();
    }

    // Start execution loop
    this.executionInterval = setInterval(() => {
      this.processExecutionQueue();
    }, 1000); // Process queue every second

    logger.info('Execution Service started');
  }

  public async stop() {
    if (!this.isRunning) return;

    logger.info('Stopping Execution Service...');
    this.isRunning = false;

    if (this.executionInterval) {
      clearInterval(this.executionInterval);
      this.executionInterval = null;
    }

    // Stop execution queue
    if (this.executionQueue) {
      await this.executionQueue.stop();
    }

    // Wait for active trades to complete or timeout
    await this.waitForActiveTrades();

    logger.info('Execution Service stopped');
  }

  public pause() {
    this.isPaused = true;
    logger.info('Execution Service paused');
  }

  public resume() {
    this.isPaused = false;
    logger.info('Execution Service resumed');
  }

  public async evaluateOpportunity(opportunity: ArbitrageOpportunity) {
    try {
      // Strict profit validation before adding to queue
      if (this.profitValidationService) {
        const profitValidation = await this.profitValidationService.validatePreExecution(
          opportunity,
          opportunity.validationResult
        );

        if (!profitValidation.isValid) {
          logger.info(`Opportunity ${opportunity.id} rejected by profit validation: ${profitValidation.reason}`);
          return;
        }

        logger.info(`Opportunity ${opportunity.id} passed profit validation - predicted profit: $${profitValidation.predictedProfit.toFixed(2)}`);
      }

      // Check if we're already trading this pair
      const isAlreadyTrading = Array.from(this.activeTrades.values()).some(trade =>
        trade.assets.some(asset => opportunity.assets.includes(asset)) &&
        trade.status === TradeStatus.EXECUTING
      );

      if (isAlreadyTrading) {
        logger.debug(`Already trading similar assets for opportunity ${opportunity.id}`);
        return;
      }

      // Add to profit-prioritized execution queue
      if (this.executionQueue) {
        const added = this.executionQueue.addOpportunity(opportunity, opportunity.validationResult);
        if (added) {
          logger.info(`Opportunity ${opportunity.id} added to profit-prioritized execution queue`);
        } else {
          logger.warn(`Failed to add opportunity ${opportunity.id} to execution queue`);
        }
      } else {
        logger.error('Execution queue not initialized');
      }

    } catch (error) {
      logger.error('Error evaluating opportunity:', error);
    }
  }

  private async processExecutionQueue() {
    if (!this.isRunning || this.isPaused || !this.executionQueue) {
      return;
    }

    // Get next opportunity from profit-prioritized queue
    const queuedOpportunity = this.executionQueue.getNextOpportunity();
    if (!queuedOpportunity) return;

    const opportunity = queuedOpportunity.opportunity;

    // Check if opportunity is still valid (not too old)
    const age = Date.now() - opportunity.timestamp;
    if (age > 30000) { // 30 seconds
      logger.debug(`Opportunity ${opportunity.id} too old, skipping`);
      this.executionQueue.markTradeCompleted(opportunity.id, false);
      return;
    }

    // Execute the trade
    await this.executeTrade(opportunity, queuedOpportunity);
  }

  private async executeTrade(opportunity: ArbitrageOpportunity, queuedOpportunity?: QueuedOpportunity): Promise<ExecutionResult> {
    const tradeId = `trade_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const trade: Trade = {
      id: tradeId,
      opportunityId: opportunity.id,
      type: opportunity.type,
      assets: opportunity.assets,
      exchanges: opportunity.exchanges,
      executedProfit: 0,
      gasFees: 0,
      status: TradeStatus.PENDING,
      timestamp: Date.now(),
      network: opportunity.network
    };

    this.activeTrades.set(tradeId, trade);
    this.emit('trade', trade);

    try {
      logger.info(`Executing ${opportunity.type} trade ${tradeId} for ${opportunity.assets.join('/')}`);
      
      // Update status to executing
      trade.status = TradeStatus.EXECUTING;
      this.emit('tradeUpdate', trade);

      let result: ExecutionResult;

      switch (opportunity.type) {
        case ArbitrageType.INTRA_CHAIN:
          result = await this.executeIntraChainArbitrage(opportunity, trade);
          break;
        case ArbitrageType.CROSS_CHAIN:
          result = await this.executeCrossChainArbitrage(opportunity, trade);
          break;
        case ArbitrageType.TRIANGULAR:
          result = await this.executeTriangularArbitrage(opportunity, trade);
          break;
        default:
          throw new Error(`Unsupported arbitrage type: ${opportunity.type}`);
      }

      if (result.success) {
        trade.status = TradeStatus.SUCCESS;
        trade.txHash = result.txHash;
        trade.executionTime = Date.now() - trade.timestamp;

        // Post-execution profit validation
        if (this.profitValidationService) {
          const postValidation = await this.profitValidationService.validatePostExecution(opportunity, trade);
          trade.profitValidationResult = postValidation;

          if (!postValidation.postExecutionPassed) {
            logger.warn(`Trade ${tradeId} failed post-execution profit validation - actual profit: $${postValidation.actualProfit.toFixed(2)}`);
          } else {
            logger.info(`Trade ${tradeId} passed post-execution validation - profit accuracy: ${postValidation.profitAccuracy.toFixed(2)}%`);
          }
        }

        logger.info(`Trade ${tradeId} executed successfully with profit $${trade.executedProfit.toFixed(2)}`);
      } else {
        trade.status = TradeStatus.FAILED;
        trade.errorMessage = result.error;
        logger.warn(`Trade ${tradeId} failed: ${result.error}`);
      }

      // Mark trade as completed in queue
      if (this.executionQueue) {
        this.executionQueue.markTradeCompleted(opportunity.id, result.success);
      }

      // Record performance data for ML learning
      await this.recordTradePerformance(trade, opportunity);

      this.emit('tradeUpdate', trade);
      return result;

    } catch (error) {
      logger.error(`Error executing trade ${tradeId}:`, error);

      trade.status = TradeStatus.FAILED;
      trade.errorMessage = error instanceof Error ? error.message : 'Unknown error';

      // Record failed trade performance for ML learning
      await this.recordTradePerformance(trade, opportunity);

      this.emit('tradeUpdate', trade);

      return {
        success: false,
        error: trade.errorMessage
      };
    }
  }

  private async recordTradePerformance(trade: Trade, opportunity: ArbitrageOpportunity) {
    if (!this.mlLearningService) {
      return; // ML service not available
    }

    try {
      // Gather execution parameters
      const executionParameters = {
        gas_limit: opportunity.estimatedGas || 300000,
        slippage_tolerance: this.maxSlippage,
        position_size: Math.abs(trade.executedProfit) + trade.gasFees,
        max_execution_time: 30000
      };

      // Gather market conditions (simplified for demo)
      const marketConditions = {
        volatility: Math.random() * 30, // Would be real market data
        liquidity: 1000000 + Math.random() * 5000000,
        network_congestion: Math.random() * 100,
        gas_price: trade.gasFees / (opportunity.estimatedGas || 300000),
        market_regime: this.mlLearningService.getCurrentMarketRegime()
      };

      // Record the performance
      await this.mlLearningService.recordStrategyPerformance(
        trade,
        opportunity,
        executionParameters,
        marketConditions
      );

    } catch (error) {
      logger.error('Error recording trade performance for ML:', error);
    }
  }

  private async executeIntraChainArbitrage(
    opportunity: ArbitrageOpportunity,
    trade: Trade
  ): Promise<ExecutionResult> {
    try {
      const wallet = this.wallets.get(opportunity.network);
      if (!wallet) {
        return { success: false, error: 'No wallet configured for network' };
      }

      // Simulate trade execution for demo
      // In production, this would interact with actual smart contracts

      const simulationResult = await this.simulateIntraChainTrade(opportunity);
      if (!simulationResult.success) {
        return simulationResult;
      }

      // Check if flash loan optimization is available from validation
      const flashLoanOptimization = opportunity.validationResult?.flashLoanOptimization;

      // Create transaction (with or without flash loan)
      const transaction = await this.createArbitrageTransaction(opportunity, wallet, flashLoanOptimization);

      // Execute flash loan if needed
      let flashLoanExecution: FlashLoanExecution | undefined;
      if (flashLoanOptimization && this.flashLoanService) {
        flashLoanExecution = await this.executeWithFlashLoan(opportunity, flashLoanOptimization, wallet);
        if (!flashLoanExecution.success) {
          return {
            success: false,
            error: `Flash loan execution failed: ${flashLoanExecution.error}`
          };
        }
        trade.flashLoanExecution = flashLoanExecution;
      }

      // Submit transaction with MEV protection
      let mevResult: MEVProtectionResult | null = null;
      let txHash: string | undefined;

      if (this.mevProtectionService && config.NODE_ENV === 'production' && config.PRIVATE_KEY) {
        // Use MEV protection for real transactions
        mevResult = await this.mevProtectionService.submitTransactionWithProtection(
          opportunity,
          transaction,
          wallet
        );

        if (mevResult.success) {
          txHash = mevResult.transactionHash;
          trade.mevProtectionResult = mevResult;

          // Monitor MEV protection performance
          await this.mevProtectionService.monitorProtectionPerformance(mevResult, opportunity);
        } else {
          return {
            success: false,
            error: `MEV protection failed: ${mevResult.error}`
          };
        }
      } else {
        // Simulate execution for demo
        txHash = `0x${Math.random().toString(16).substr(2, 64)}`;
      }

      // Simulate successful execution
      const executedProfit = opportunity.potentialProfit * (0.9 + Math.random() * 0.2); // ±10% variation
      const gasFees = mevResult?.gasUsed ?
        (mevResult.gasUsed * (mevResult.effectiveGasPrice || 20)) / 1e9 * 2000 : // Real gas cost
        opportunity.estimatedGas * 20 * 1e-9 * 2000; // Estimated gas cost

      trade.executedProfit = executedProfit - gasFees;
      trade.gasFees = gasFees;

      return {
        success: true,
        trade,
        txHash
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Execution failed'
      };
    }
  }

  private async simulateIntraChainTrade(opportunity: ArbitrageOpportunity): Promise<ExecutionResult> {
    try {
      // Simulate trade validation
      await new Promise(resolve => setTimeout(resolve, 100)); // Simulate network delay

      // Check if opportunity is still profitable
      const currentProfitability = opportunity.potentialProfit * (0.8 + Math.random() * 0.4);
      
      if (currentProfitability < parseFloat(config.MIN_PROFIT_THRESHOLD)) {
        return {
          success: false,
          error: 'Opportunity no longer profitable'
        };
      }

      // Simulate slippage check
      if (opportunity.slippage > this.maxSlippage) {
        return {
          success: false,
          error: 'Slippage too high'
        };
      }

      return { success: true };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Simulation failed'
      };
    }
  }

  private async executeCrossChainArbitrage(
    opportunity: ArbitrageOpportunity, 
    trade: Trade
  ): Promise<ExecutionResult> {
    try {
      // Cross-chain arbitrage is more complex and involves bridging
      // This is a simplified implementation
      
      logger.info(`Executing cross-chain arbitrage for ${opportunity.assets.join('/')}`);
      
      // Simulate cross-chain execution
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate longer execution time
      
      const executedProfit = opportunity.potentialProfit * 0.85; // Account for bridge costs
      const gasFees = opportunity.estimatedGas * 1.5; // Higher gas for cross-chain

      trade.executedProfit = executedProfit - gasFees;
      trade.gasFees = gasFees;

      return {
        success: true,
        trade,
        txHash: `0x${Math.random().toString(16).substr(2, 64)}`
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Cross-chain execution failed'
      };
    }
  }

  private async executeTriangularArbitrage(
    opportunity: ArbitrageOpportunity, 
    trade: Trade
  ): Promise<ExecutionResult> {
    try {
      logger.info(`Executing triangular arbitrage for ${opportunity.assets.join('/')}`);
      
      // Simulate triangular arbitrage execution
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const executedProfit = opportunity.potentialProfit * 0.9; // Account for multiple swaps
      const gasFees = opportunity.estimatedGas * 3; // Higher gas for multiple swaps

      trade.executedProfit = executedProfit - gasFees;
      trade.gasFees = gasFees;

      return {
        success: true,
        trade,
        txHash: `0x${Math.random().toString(16).substr(2, 64)}`
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Triangular execution failed'
      };
    }
  }

  /**
   * Execute arbitrage with flash loan
   */
  private async executeWithFlashLoan(
    opportunity: ArbitrageOpportunity,
    flashLoanOptimization: FlashLoanOptimization,
    wallet: ethers.Wallet
  ): Promise<FlashLoanExecution> {

    const startTime = Date.now();

    try {
      logger.info(`Executing flash loan arbitrage for ${opportunity.id} using ${flashLoanOptimization.recommendedProvider}`);

      // In production, this would interact with actual flash loan contracts
      // For demo, simulate flash loan execution

      const executionTime = Date.now() - startTime;

      // Simulate successful flash loan execution
      const flashLoanExecution: FlashLoanExecution = {
        provider: flashLoanOptimization.recommendedProvider,
        strategy: flashLoanOptimization.recommendedStrategy,
        asset: opportunity.assets[0],
        amount: flashLoanOptimization.optimalAmount,
        fee: flashLoanOptimization.totalCost,
        gasUsed: 250000, // Estimated gas for flash loan
        executionTime,
        success: true,
        transactionHash: `0x${Math.random().toString(16).substr(2, 64)}`,
        profitAfterFees: flashLoanOptimization.expectedProfit
      };

      logger.info(`Flash loan execution completed for ${opportunity.id}: profit $${flashLoanExecution.profitAfterFees.toFixed(2)}`);

      return flashLoanExecution;

    } catch (error) {
      logger.error(`Flash loan execution failed for ${opportunity.id}:`, error);

      return {
        provider: flashLoanOptimization.recommendedProvider,
        strategy: flashLoanOptimization.recommendedStrategy,
        asset: opportunity.assets[0],
        amount: flashLoanOptimization.optimalAmount,
        fee: 0,
        gasUsed: 0,
        executionTime: Date.now() - startTime,
        success: false,
        error: error instanceof Error ? error.message : 'Flash loan execution failed',
        profitAfterFees: 0
      };
    }
  }

  /**
   * Create arbitrage transaction for MEV protection
   */
  private async createArbitrageTransaction(
    opportunity: ArbitrageOpportunity,
    wallet: ethers.Wallet,
    flashLoanOptimization?: FlashLoanOptimization
  ): Promise<string> {
    try {
      // In production, this would create the actual arbitrage transaction
      // For demo, we'll create a simple transaction

      const provider = this.providers.get(opportunity.network);
      if (!provider) {
        throw new Error(`No provider for network: ${opportunity.network}`);
      }

      // Get current gas price and nonce
      const feeData = await provider.getFeeData();
      const nonce = await wallet.getNonce();

      // Create transaction object
      const transaction = {
        to: wallet.address, // Placeholder - would be arbitrage contract
        value: 0,
        gasLimit: opportunity.estimatedGas || 300000,
        gasPrice: feeData.gasPrice,
        nonce,
        data: '0x' // Placeholder - would be arbitrage function call
      };

      // Sign the transaction
      const signedTransaction = await wallet.signTransaction(transaction);

      return signedTransaction;

    } catch (error) {
      logger.error('Failed to create arbitrage transaction:', error);
      throw error;
    }
  }

  private async waitForActiveTrades() {
    const timeout = 30000; // 30 seconds timeout
    const startTime = Date.now();

    while (this.activeTrades.size > 0 && Date.now() - startTime < timeout) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    if (this.activeTrades.size > 0) {
      logger.warn(`${this.activeTrades.size} trades still active after timeout`);
    }
  }

  public getTrades(): Trade[] {
    return Array.from(this.activeTrades.values())
      .sort((a, b) => b.timestamp - a.timestamp);
  }

  public getTrade(id: string): Trade | undefined {
    return this.activeTrades.get(id);
  }

  public getQueueSize(): number {
    return this.executionQueue ? this.executionQueue.getQueueStats().queueSize : 0;
  }

  public getActiveTradesCount(): number {
    return Array.from(this.activeTrades.values())
      .filter(trade => trade.status === TradeStatus.EXECUTING).length;
  }

  public isHealthy(): boolean {
    return this.isRunning && !this.isPaused;
  }

  public getStats() {
    const statusStats: Record<string, number> = {};
    this.activeTrades.forEach(trade => {
      statusStats[trade.status] = (statusStats[trade.status] || 0) + 1;
    });

    const totalProfit = Array.from(this.activeTrades.values())
      .filter(trade => trade.status === TradeStatus.SUCCESS)
      .reduce((sum, trade) => sum + trade.executedProfit, 0);

    const successfulTrades = Array.from(this.activeTrades.values())
      .filter(trade => trade.status === TradeStatus.SUCCESS).length;

    const totalTrades = this.activeTrades.size;
    const successRate = totalTrades > 0 ? (successfulTrades / totalTrades) * 100 : 0;

    return {
      totalTrades,
      successfulTrades,
      successRate,
      totalProfit,
      queueSize: this.getQueueSize(),
      activeTrades: this.getActiveTradesCount(),
      statusStats,
      isRunning: this.isRunning,
      isPaused: this.isPaused
    };
  }
}
