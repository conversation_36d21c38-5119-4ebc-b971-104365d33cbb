{"_format": "hh-sol-artifact-1", "contractName": "LiquidityChecker", "sourceName": "contracts/LiquidityChecker.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "reserve0", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "reserve1", "type": "uint256"}], "name": "LiquidityUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"indexed": false, "internalType": "address", "name": "token0", "type": "address"}, {"indexed": false, "internalType": "address", "name": "token1", "type": "address"}], "name": "PoolAdded", "type": "event"}, {"inputs": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "addPool", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "poolAddresses", "type": "address[]"}], "name": "batchUpdatePools", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "address", "name": "tokenIn", "type": "address"}, {"internalType": "uint256", "name": "amountIn", "type": "uint256"}], "name": "calculatePriceImpact", "outputs": [{"internalType": "uint256", "name": "priceImpact", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "getPoolInfo", "outputs": [{"components": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "address", "name": "token0", "type": "address"}, {"internalType": "address", "name": "token1", "type": "address"}, {"internalType": "uint256", "name": "reserve0", "type": "uint256"}, {"internalType": "uint256", "name": "reserve1", "type": "uint256"}, {"internalType": "uint256", "name": "lastUpdated", "type": "uint256"}], "internalType": "struct LiquidityChecker.PoolInfo", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "getPoolReserves", "outputs": [{"internalType": "uint256", "name": "reserve0", "type": "uint256"}, {"internalType": "uint256", "name": "reserve1", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getTrackedPools", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "address", "name": "tokenIn", "type": "address"}, {"internalType": "uint256", "name": "amountIn", "type": "uint256"}, {"internalType": "uint256", "name": "minLiquidityRatio", "type": "uint256"}], "name": "hasSufficientLiquidity", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "pools", "outputs": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "address", "name": "token0", "type": "address"}, {"internalType": "address", "name": "token1", "type": "address"}, {"internalType": "uint256", "name": "reserve0", "type": "uint256"}, {"internalType": "uint256", "name": "reserve1", "type": "uint256"}, {"internalType": "uint256", "name": "lastUpdated", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "trackedPools", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "updatePoolLiquidity", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "0x60806040908082526004918236101561001757600080fd5b600091823560e01c90816306bfa938146109835750806312e8c7f91461085657806316c4bcaf1461081757806323bfa792146107d65780634bee939514610748578063721318b014610584578063a127707314610498578063a4063dbc14610423578063b2098fa2146103695763d914cd4b1461009357600080fd5b346103655760209081600319360112610361576001600160a01b03806100b7610a59565b16928315610328578385528481528183862054166102ef578251630dfe168160e01b81529181838881885afa9283156102e55786936102c6575b50835163d21220a760e01b815282818981895afa9081156102bc57879161028f575b508451630240bc6b60e21b81526060818a818a5afa9081156102855787899594939260059287908892610253575b5089519461014e86610ad7565b838652868887019a16998a8152878c81818a0194169b8c855260608a01956001600160701b03809116875260808b019716875260a08a01974289528152808c5220975116906bffffffffffffffffffffffff60a01b9182895416178855886001890191511682825416179055876002880192511690825416179055516003850155518b8401555191015560015468010000000000000000811015610240577f4c61bab17e59e06eb29c0659ba5f68dc5bc003d57587a7280d98d532d2bf312a969798508060016102219201600155610a8a565b819291549060031b9189831b921b19161790558351928352820152a280f35b634e487b7160e01b885260418952602488fd5b9050610276915060603d811161027e575b61026e8183610b09565b810190610b5e565b509038610141565b503d610264565b86513d8a823e3d90fd5b6102af9150833d85116102b5575b6102a78183610b09565b810190610b2b565b38610113565b503d61029d565b85513d89823e3d90fd5b6102de919350823d84116102b5576102a78183610b09565b91386100f1565b84513d88823e3d90fd5b8560649184519162461bcd60e51b83528201526014602482015273141bdbdb08185b1c9958591e481d1c9858dad95960621b6044820152fd5b8560649184519162461bcd60e51b83528201526014602482015273496e76616c696420706f6f6c206164647265737360601b6044820152fd5b8280fd5b5080fd5b503461036557816003193601126103655780516001805480835281855260208084019592939186907fb10e2d527612073b26eecdfd717e6a320cf44b4afac2b0732d9fcbe2b7fa0cf690855b81811061040757505050826103cb910383610b09565b8451948186019282875251809352850195925b8281106103eb5785870386f35b83516001600160a01b03168752958101959281019284016103de565b82546001600160a01b03168452928401929187019187016103b5565b5082346103615760203660031901126103615760c0926001600160a01b03919083908361044e610a59565b1681528060205220908282541693836001840154169360028401541690600560038501549385015494015494815196875260208701528501526060840152608083015260a0820152f35b50829034610361576020366003190112610361576001600160a01b03906104d6826104c1610a59565b16928386528560205282862054161515610b94565b8051630240bc6b60e21b81526060818581865afa93841561057a577f6ba1694b349793ed1971f7a177834febc52502ceee1660a3bb8e569e20727706939486928791610555575b506001600160701b0380931692868852876020528488209184600384015516809282015560054291015582519182526020820152a280f35b905061057091925060603d811161027e5761026e8183610b09565b509190918761051d565b82513d87823e3d90fd5b5090346107455760603660031901126107455761059f610a59565b6105a7610a74565b916044359160018060a01b0380911693848352826020526105ce8287852054161515610b94565b8551630240bc6b60e21b8152916060838981895afa95861561073b57889085948698610715575b50602090895192838092630dfe168160e01b82525afa90811561070b5790829186916106ed575b50169116146000146106db576001600160701b0380911693165b6103e59081840290841592858304148317156106b5576106569082610bd3565b6103e8918287029287840414871517156106c85782018092116106b5579061067d91610cdc565b506127108084029384041417156106a25760208461069b8585610cdc565b9051908152f35b634e487b7160e01b815260118552602490fd5b634e487b7160e01b845260118852602484fd5b634e487b7160e01b855260118952602485fd5b6001600160701b039384169316610636565b610705915060203d81116102b5576102a78183610b09565b3861061c565b88513d87823e3d90fd5b6107329198506020955060603d811161027e5761026e8183610b09565b509790946105f5565b87513d86823e3d90fd5b80fd5b509190346107455760203660031901126107455760606001600160a01b0361076e610a59565b16845193848092630240bc6b60e21b82525afa9182156107ca5781926107a8575b508251916001600160701b038092168352166020820152f35b90506107c2915060603d811161027e5761026e8183610b09565b50903861078f565b508251903d90823e3d90fd5b5082346103615760203660031901126103615735916001548310156107455750610801602092610a8a565b905491519160018060a01b039160031b1c168152f35b50346103655760803660031901126103655760209061084d610837610a59565b61083f610a74565b906064359160443591610bfc565b90519015158152f35b508234610361576020908160031936011261097f5780359167ffffffffffffffff9384841161097b573660238501121561097b57838301359480861161097757602495868601958736918360051b01011161097357875b8181106108b8578880f35b6001600160a01b03806108d46108cf84868c610cfc565b610d0c565b168a5289865280858b205416610909575b5060001981146108f7576001016108ad565b634e487b7160e01b8952601186528789fd5b6109176108cf83858b610cfc565b90303b1561096f57855163a127707360e01b815291168782015289818a8183305af1801561096557156108e557838111610953578452896108e5565b634e487b7160e01b8a5260418752888afd5b85513d8c823e3d90fd5b8a80fd5b8780fd5b8680fd5b8580fd5b8380fd5b929390503461097f57602036600319011261097f57818460c09560a06109a7610a59565b966109b181610ad7565b8281528260208201528285820152826060820152826080820152015260018060a01b0380951681528060205220908251926109eb84610ad7565b8483541694858552806001850154166020860190815281806002870154169184880192835260a0600560038901549760608b019889528901549860808b01998a52015498019788528451988952511660208801525116908501525160608401525160808301525160a0820152f35b600435906001600160a01b0382168203610a6f57565b600080fd5b602435906001600160a01b0382168203610a6f57565b600154811015610ac15760016000527fb10e2d527612073b26eecdfd717e6a320cf44b4afac2b0732d9fcbe2b7fa0cf60190600090565b634e487b7160e01b600052603260045260246000fd5b60c0810190811067ffffffffffffffff821117610af357604052565b634e487b7160e01b600052604160045260246000fd5b90601f8019910116810190811067ffffffffffffffff821117610af357604052565b90816020910312610a6f57516001600160a01b0381168103610a6f5790565b51906001600160701b0382168203610a6f57565b90816060910312610a6f57610b7281610b4a565b916040610b8160208401610b4a565b92015163ffffffff81168103610a6f5790565b15610b9b57565b60405162461bcd60e51b815260206004820152601060248201526f141bdbdb081b9bdd081d1c9858dad95960821b6044820152606490fd5b81810292918115918404141715610be657565b634e487b7160e01b600052601160045260246000fd5b92919260018060a01b038091169160009280845283602052610c25836040862054161515610b94565b835282602052604083209060405192610c3d84610ad7565b8083541684528060018401541690816020860152806002850154169283604087015260038501549586606082015260a06005600488015497886080850152015491015216908114600014610cc757505050905b606484029380850460641490151715610cb3575090610cae91610bd3565b101590565b634e487b7160e01b81526011600452602490fd5b91925003610cd55790610c90565b5091505090565b8115610ce6570490565b634e487b7160e01b600052601260045260246000fd5b9190811015610ac15760051b0190565b356001600160a01b0381168103610a6f579056fea2646970667358221220e863e2761d2604a8a4816642ba3bb3ec955dc0553f233e23e160d561f215dae764736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}