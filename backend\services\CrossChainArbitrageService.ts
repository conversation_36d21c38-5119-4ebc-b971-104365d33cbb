import { MultiChainService, CrossChainOpportunity, NetworkConfig } from './MultiChainService.js';
import { MarketDataService } from './MarketDataService.js';
import logger from '../utils/logger.js';

export interface PriceData {
  networkId: string;
  dex: string;
  price: number;
  liquidity: number;
  slippage: number;
  timestamp: number;
}

export interface ArbitrageCalculation {
  tokenSymbol: string;
  sourceNetwork: string;
  targetNetwork: string;
  sourceDex: string;
  targetDex: string;
  sourcePrice: number;
  targetPrice: number;
  priceDifference: number;
  priceDifferencePercentage: number;
  gasCosts: {
    source: number;
    target: number;
    bridge: number;
    total: number;
  };
  bridgeFee: number;
  slippageImpact: number;
  netProfit: number;
  netProfitPercentage: number;
  confidence: number;
  riskScore: number;
  executionTime: number;
  minTradeSize: number;
  maxTradeSize: number;
}

export class CrossChainArbitrageService {
  private multiChainService: MultiChainService;
  private marketDataService: MarketDataService;
  private priceFeeds: Map<string, Map<string, PriceData[]>> = new Map(); // token -> network -> prices
  private opportunities: CrossChainOpportunity[] = [];
  private updateInterval: NodeJS.Timeout | null = null;

  constructor(multiChainService: MultiChainService, marketDataService: MarketDataService) {
    this.multiChainService = multiChainService;
    this.marketDataService = marketDataService;
    logger.info('CrossChainArbitrageService initialized');
  }

  /**
   * Start monitoring cross-chain arbitrage opportunities
   */
  startMonitoring(intervalMs: number = 60000): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }

    // Initial scan
    this.scanForOpportunities().catch(error => {
      logger.error('Initial arbitrage scan failed:', error);
    });

    // Schedule regular scans
    this.updateInterval = setInterval(async () => {
      try {
        await this.scanForOpportunities();
      } catch (error) {
        logger.error('Scheduled arbitrage scan failed:', error);
      }
    }, intervalMs);

    logger.info(`Cross-chain arbitrage monitoring started with ${intervalMs}ms interval`);
  }

  /**
   * Stop monitoring
   */
  stopMonitoring(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
      logger.info('Cross-chain arbitrage monitoring stopped');
    }
  }

  /**
   * Scan for cross-chain arbitrage opportunities
   */
  async scanForOpportunities(): Promise<void> {
    try {
      logger.info('Scanning for cross-chain arbitrage opportunities...');
      
      const targetTokens = this.multiChainService.getTargetTokens();
      const networks = this.multiChainService.getSupportedNetworks();
      const newOpportunities: CrossChainOpportunity[] = [];

      for (const token of targetTokens) {
        // Get price data for this token across all networks
        const tokenPrices = await this.getTokenPricesAcrossNetworks(token.symbol);
        
        if (tokenPrices.size < 2) continue; // Need at least 2 networks for arbitrage

        // Find arbitrage opportunities between network pairs
        const networkIds = Array.from(tokenPrices.keys());
        
        for (let i = 0; i < networkIds.length; i++) {
          for (let j = i + 1; j < networkIds.length; j++) {
            const sourceNetworkId = networkIds[i];
            const targetNetworkId = networkIds[j];
            
            const sourcePrices = tokenPrices.get(sourceNetworkId) || [];
            const targetPrices = tokenPrices.get(targetNetworkId) || [];
            
            if (sourcePrices.length === 0 || targetPrices.length === 0) continue;

            // Find best prices on each network
            const bestSourcePrice = this.getBestPrice(sourcePrices, 'sell');
            const bestTargetPrice = this.getBestPrice(targetPrices, 'buy');
            
            if (!bestSourcePrice || !bestTargetPrice) continue;

            // Calculate arbitrage opportunity
            const calculation = await this.calculateArbitrage(
              token.symbol,
              sourceNetworkId,
              targetNetworkId,
              bestSourcePrice,
              bestTargetPrice
            );

            if (calculation && calculation.netProfitPercentage > 0.5) { // Minimum 0.5% profit
              const opportunity: CrossChainOpportunity = {
                id: `arb_${token.symbol}_${sourceNetworkId}_${targetNetworkId}_${Date.now()}`,
                tokenSymbol: calculation.tokenSymbol,
                sourceNetwork: calculation.sourceNetwork,
                targetNetwork: calculation.targetNetwork,
                sourceDex: calculation.sourceDex,
                targetDex: calculation.targetDex,
                sourcePrice: calculation.sourcePrice,
                targetPrice: calculation.targetPrice,
                priceDifference: calculation.priceDifference,
                priceDifferencePercentage: calculation.priceDifferencePercentage,
                estimatedProfit: calculation.netProfit,
                gasCosts: calculation.gasCosts,
                bridgeFee: calculation.bridgeFee,
                slippageImpact: calculation.slippageImpact,
                netProfit: calculation.netProfit,
                netProfitPercentage: calculation.netProfitPercentage,
                confidence: calculation.confidence,
                riskScore: calculation.riskScore,
                executionTime: calculation.executionTime,
                minTradeSize: calculation.minTradeSize,
                maxTradeSize: calculation.maxTradeSize,
                created_at: new Date().toISOString()
              };

              newOpportunities.push(opportunity);
            }
          }
        }
      }

      // Update opportunities list
      this.opportunities = newOpportunities.sort((a, b) => b.netProfitPercentage - a.netProfitPercentage);
      
      logger.info(`Found ${this.opportunities.length} cross-chain arbitrage opportunities`);
      
    } catch (error) {
      logger.error('Error scanning for arbitrage opportunities:', error);
    }
  }

  /**
   * Get token prices across all networks
   */
  private async getTokenPricesAcrossNetworks(tokenSymbol: string): Promise<Map<string, PriceData[]>> {
    const priceMap = new Map<string, PriceData[]>();
    const networks = this.multiChainService.getSupportedNetworks();

    for (const network of networks) {
      const prices = await this.getTokenPricesOnNetwork(tokenSymbol, network.id);
      if (prices.length > 0) {
        priceMap.set(network.id, prices);
      }
    }

    return priceMap;
  }

  /**
   * Get token prices on a specific network
   */
  private async getTokenPricesOnNetwork(tokenSymbol: string, networkId: string): Promise<PriceData[]> {
    const network = this.multiChainService.getNetwork(networkId);
    if (!network) return [];

    const prices: PriceData[] = [];
    
    // Simulate price data from different DEXes (in real implementation, would call actual DEX APIs)
    for (const dex of network.dexes) {
      const basePrice = await this.getBaseTokenPrice(tokenSymbol);
      if (basePrice > 0) {
        // Add some variance to simulate different DEX prices
        const variance = (Math.random() - 0.5) * 0.02; // ±1% variance
        const dexPrice = basePrice * (1 + variance);
        
        prices.push({
          networkId,
          dex,
          price: dexPrice,
          liquidity: Math.random() * 1000000 + 100000, // $100k - $1M liquidity
          slippage: Math.random() * 0.5 + 0.1, // 0.1% - 0.6% slippage
          timestamp: Date.now()
        });
      }
    }

    return prices;
  }

  /**
   * Get base token price from market data
   */
  private async getBaseTokenPrice(tokenSymbol: string): Promise<number> {
    try {
      // Map token symbols to CoinGecko IDs
      const tokenMap: { [key: string]: string } = {
        'BTC': 'bitcoin',
        'WBTC': 'wrapped-bitcoin',
        'ETH': 'ethereum',
        'WETH': 'weth',
        'USDT': 'tether',
        'USDC': 'usd-coin',
        'BUSD': 'binance-usd',
        'BNB': 'binancecoin',
        'SOL': 'solana',
        'WSOL': 'wrapped-solana',
        'MATIC': 'matic-network',
        'WMATIC': 'wmatic',
        'ADA': 'cardano',
        'SUI': 'sui',
        'AVAX': 'avalanche-2',
        'HYPE': 'hyperliquid'
      };

      const coingeckoId = tokenMap[tokenSymbol];
      if (!coingeckoId) return 0;

      // Use existing market data service
      const prices = await this.marketDataService.getTokenPrices([coingeckoId]);
      return prices[coingeckoId]?.usd || 0;
    } catch (error) {
      logger.error(`Error getting price for ${tokenSymbol}:`, error);
      return 0;
    }
  }

  /**
   * Get best price from available options
   */
  private getBestPrice(prices: PriceData[], side: 'buy' | 'sell'): PriceData | null {
    if (prices.length === 0) return null;

    return prices.reduce((best, current) => {
      if (side === 'buy') {
        return current.price < best.price ? current : best;
      } else {
        return current.price > best.price ? current : best;
      }
    });
  }

  /**
   * Calculate arbitrage opportunity
   */
  private async calculateArbitrage(
    tokenSymbol: string,
    sourceNetwork: string,
    targetNetwork: string,
    sourcePrice: PriceData,
    targetPrice: PriceData
  ): Promise<ArbitrageCalculation | null> {
    try {
      const priceDifference = targetPrice.price - sourcePrice.price;
      const priceDifferencePercentage = (priceDifference / sourcePrice.price) * 100;

      // Skip if price difference is negative or too small
      if (priceDifferencePercentage < 0.1) return null;

      // Calculate costs
      const sourceGasCost = this.multiChainService.calculateGasCosts(sourceNetwork, 200000);
      const targetGasCost = this.multiChainService.calculateGasCosts(targetNetwork, 200000);
      const bridgeGasCost = this.multiChainService.calculateGasCosts(sourceNetwork, 300000);
      
      const totalGasCosts = sourceGasCost + targetGasCost + bridgeGasCost;
      
      // Assume $1000 trade size for calculation
      const tradeSize = 1000;
      const bridgeFee = this.multiChainService.calculateBridgeFee(sourceNetwork, tradeSize);
      const slippageImpact = (sourcePrice.slippage + targetPrice.slippage) / 100 * tradeSize;
      
      const grossProfit = (priceDifference / sourcePrice.price) * tradeSize;
      const netProfit = grossProfit - totalGasCosts - bridgeFee - slippageImpact;
      const netProfitPercentage = (netProfit / tradeSize) * 100;

      // Calculate confidence and risk scores
      const confidence = this.calculateConfidence(sourcePrice, targetPrice, sourceNetwork, targetNetwork);
      const riskScore = this.calculateRiskScore(sourceNetwork, targetNetwork, tokenSymbol);
      
      // Calculate trade size limits
      const minLiquidity = Math.min(sourcePrice.liquidity, targetPrice.liquidity);
      const minTradeSize = Math.max(100, totalGasCosts * 10); // Minimum to cover costs
      const maxTradeSize = Math.min(50000, minLiquidity * 0.1); // Max 10% of liquidity

      const executionTime = this.multiChainService.estimateExecutionTime(sourceNetwork, targetNetwork);

      return {
        tokenSymbol,
        sourceNetwork,
        targetNetwork,
        sourceDex: sourcePrice.dex,
        targetDex: targetPrice.dex,
        sourcePrice: sourcePrice.price,
        targetPrice: targetPrice.price,
        priceDifference,
        priceDifferencePercentage,
        gasCosts: {
          source: sourceGasCost,
          target: targetGasCost,
          bridge: bridgeGasCost,
          total: totalGasCosts
        },
        bridgeFee,
        slippageImpact,
        netProfit,
        netProfitPercentage,
        confidence,
        riskScore,
        executionTime,
        minTradeSize,
        maxTradeSize
      };
    } catch (error) {
      logger.error('Error calculating arbitrage:', error);
      return null;
    }
  }

  /**
   * Calculate confidence score
   */
  private calculateConfidence(
    sourcePrice: PriceData,
    targetPrice: PriceData,
    sourceNetwork: string,
    targetNetwork: string
  ): number {
    let confidence = 100;

    // Reduce confidence based on slippage
    confidence -= (sourcePrice.slippage + targetPrice.slippage) * 10;

    // Reduce confidence based on liquidity
    const minLiquidity = Math.min(sourcePrice.liquidity, targetPrice.liquidity);
    if (minLiquidity < 100000) confidence -= 20;
    if (minLiquidity < 50000) confidence -= 30;

    // Reduce confidence for newer/riskier networks
    const riskierNetworks = ['sui', 'base'];
    if (riskierNetworks.includes(sourceNetwork) || riskierNetworks.includes(targetNetwork)) {
      confidence -= 15;
    }

    return Math.max(0, Math.min(100, confidence));
  }

  /**
   * Calculate risk score
   */
  private calculateRiskScore(sourceNetwork: string, targetNetwork: string, tokenSymbol: string): number {
    let risk = 0;

    // Network risk
    const networkRisk: { [key: string]: number } = {
      'ethereum': 5,
      'bsc': 10,
      'polygon': 15,
      'arbitrum': 10,
      'optimism': 10,
      'avalanche': 20,
      'solana': 25,
      'base': 30,
      'fantom': 35,
      'sui': 40
    };

    risk += (networkRisk[sourceNetwork] || 50) + (networkRisk[targetNetwork] || 50);

    // Token risk
    const stablecoins = ['USDT', 'USDC', 'BUSD'];
    if (!stablecoins.includes(tokenSymbol)) {
      risk += 20; // Higher risk for volatile tokens
    }

    // Bridge risk
    risk += 15; // Base bridge risk

    return Math.min(100, risk);
  }

  /**
   * Get current opportunities
   */
  getOpportunities(limit: number = 20): CrossChainOpportunity[] {
    return this.opportunities.slice(0, limit);
  }

  /**
   * Get opportunities for specific token
   */
  getOpportunitiesForToken(tokenSymbol: string): CrossChainOpportunity[] {
    return this.opportunities.filter(opp => opp.tokenSymbol === tokenSymbol);
  }

  /**
   * Get opportunities between specific networks
   */
  getOpportunitiesBetweenNetworks(sourceNetwork: string, targetNetwork: string): CrossChainOpportunity[] {
    return this.opportunities.filter(opp => 
      opp.sourceNetwork === sourceNetwork && opp.targetNetwork === targetNetwork
    );
  }

  /**
   * Get statistics
   */
  getStatistics() {
    const opportunities = this.opportunities;
    
    if (opportunities.length === 0) {
      return {
        totalOpportunities: 0,
        avgProfitPercentage: 0,
        avgExecutionTime: 0,
        topTokens: [],
        topNetworkPairs: [],
        totalPotentialProfit: 0
      };
    }

    const avgProfitPercentage = opportunities.reduce((sum, opp) => sum + opp.netProfitPercentage, 0) / opportunities.length;
    const avgExecutionTime = opportunities.reduce((sum, opp) => sum + opp.executionTime, 0) / opportunities.length;
    const totalPotentialProfit = opportunities.reduce((sum, opp) => sum + opp.netProfit, 0);

    // Top tokens by opportunity count
    const tokenCounts = new Map<string, number>();
    opportunities.forEach(opp => {
      tokenCounts.set(opp.tokenSymbol, (tokenCounts.get(opp.tokenSymbol) || 0) + 1);
    });
    const topTokens = Array.from(tokenCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([token, count]) => ({ token, count }));

    // Top network pairs
    const pairCounts = new Map<string, number>();
    opportunities.forEach(opp => {
      const pair = `${opp.sourceNetwork}-${opp.targetNetwork}`;
      pairCounts.set(pair, (pairCounts.get(pair) || 0) + 1);
    });
    const topNetworkPairs = Array.from(pairCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([pair, count]) => ({ pair, count }));

    return {
      totalOpportunities: opportunities.length,
      avgProfitPercentage,
      avgExecutionTime,
      topTokens,
      topNetworkPairs,
      totalPotentialProfit,
      lastUpdated: new Date().toISOString()
    };
  }
}
