// Simple integration test script
const API_BASE_URL = 'http://localhost:3001';

async function testBackendIntegration() {
  console.log('🧪 Testing Backend Integration...\n');

  // Test 1: Health Check
  try {
    console.log('1. Testing Health Endpoint...');
    const healthResponse = await fetch(`${API_BASE_URL}/health`);
    const healthData = await healthResponse.json();
    console.log('✅ Health Check:', healthData.status);
    console.log('   Services:', Object.keys(healthData.services).length, 'services detected');
  } catch (error) {
    console.log('❌ Health Check Failed:', error.message);
    return;
  }

  // Wait a bit to avoid rate limiting
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Test 2: Opportunities Endpoint
  try {
    console.log('\n2. Testing Opportunities Endpoint...');
    const oppResponse = await fetch(`${API_BASE_URL}/api/opportunities?limit=3`);
    const oppData = await oppResponse.json();
    if (oppData.success) {
      console.log('✅ Opportunities:', oppData.data.length, 'opportunities found');
      if (oppData.data.length > 0) {
        const opp = oppData.data[0];
        console.log('   Sample:', opp.type, 'opportunity with', opp.potentialProfit.toFixed(2), 'profit');
      }
    } else {
      console.log('❌ Opportunities Failed:', oppData.message);
    }
  } catch (error) {
    console.log('❌ Opportunities Failed:', error.message);
  }

  // Wait a bit to avoid rate limiting
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Test 3: Tokens Endpoint
  try {
    console.log('\n3. Testing Tokens Endpoint...');
    const tokensResponse = await fetch(`${API_BASE_URL}/api/tokens`);
    const tokensData = await tokensResponse.json();
    if (tokensData.success) {
      console.log('✅ Tokens:', tokensData.data.length, 'tokens found');
      if (tokensData.data.length > 0) {
        const token = tokensData.data[0];
        console.log('   Sample:', token.symbol, 'with safety score', token.safetyScore);
      }
    } else {
      console.log('❌ Tokens Failed:', tokensData.message);
    }
  } catch (error) {
    console.log('❌ Tokens Failed:', error.message);
  }

  // Wait a bit to avoid rate limiting
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Test 4: Analytics Endpoint
  try {
    console.log('\n4. Testing Analytics Endpoint...');
    const analyticsResponse = await fetch(`${API_BASE_URL}/api/analytics/performance`);
    const analyticsData = await analyticsResponse.json();
    if (analyticsData.success) {
      console.log('✅ Analytics:', 'Total trades:', analyticsData.data.totalTrades);
      console.log('   Net Profit:', analyticsData.data.netProfit);
      console.log('   Win Rate:', analyticsData.data.winRate + '%');
    } else {
      console.log('❌ Analytics Failed:', analyticsData.message);
    }
  } catch (error) {
    console.log('❌ Analytics Failed:', error.message);
  }

  console.log('\n🎉 Integration test completed!');
  console.log('\n📊 Frontend Dashboard should now be showing real backend data at:');
  console.log('   http://localhost:5173');
}

// Run the test
testBackendIntegration().catch(console.error);
