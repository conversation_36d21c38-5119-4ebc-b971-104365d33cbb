// Quick Supabase Connection Test
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

console.log('🗄️ Testing Supabase Connection...\n');

async function testSupabase() {
  try {
    // Create Supabase client
    const supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    console.log('✅ Supabase client created');
    console.log(`   URL: ${process.env.SUPABASE_URL}`);

    // Test connection by checking tables
    const { data: tables, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .in('table_name', ['trades', 'opportunities', 'performance_metrics', 'tokens', 'system_alerts', 'configuration']);

    if (error) {
      console.log('❌ Connection failed:', error.message);
      return false;
    }

    const tableNames = tables.map(t => t.table_name);
    console.log('✅ Connection successful');
    console.log('📋 Tables found:', tableNames.join(', '));

    if (tableNames.length === 6) {
      console.log('🎉 All required tables exist!');
      
      // Test insert into opportunities table
      const testOpportunity = {
        opportunity_id: 'test-' + Date.now(),
        type: 'intra-chain',
        assets: ['ETH', 'USDC'],
        exchanges: ['Uniswap', 'Sushiswap'],
        potential_profit: 125.75,
        profit_percentage: 3.2,
        timestamp: new Date().toISOString(),
        network: 'ethereum',
        confidence: 92.5,
        slippage: 0.15
      };

      const { error: insertError } = await supabase
        .from('opportunities')
        .insert([testOpportunity]);

      if (insertError) {
        console.log('⚠️  Test write failed:', insertError.message);
      } else {
        console.log('✅ Test write successful');
        
        // Clean up test record
        await supabase
          .from('opportunities')
          .delete()
          .eq('opportunity_id', testOpportunity.opportunity_id);
        console.log('🧹 Test record cleaned up');
      }

      return true;
    } else {
      console.log('⚠️  Missing tables. Please run the SQL schema.');
      console.log('   Expected: trades, opportunities, performance_metrics, tokens, system_alerts, configuration');
      return false;
    }

  } catch (error) {
    console.log('❌ Test failed:', error.message);
    return false;
  }
}

testSupabase().then(success => {
  if (success) {
    console.log('\n🎉 Supabase is fully configured and working!');
  } else {
    console.log('\n📚 Next steps:');
    console.log('1. Go to your Supabase dashboard');
    console.log('2. Open SQL Editor');
    console.log('3. Run the schema from database/supabase-schema.sql');
  }
});
