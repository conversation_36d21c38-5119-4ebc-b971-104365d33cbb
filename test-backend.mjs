// Simple test backend to verify connectivity
import http from 'http';

console.log('Starting test backend...');

const server = http.createServer((req, res) => {
  console.log(`Request: ${req.method} ${req.url}`);
  
  res.writeHead(200, {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
  });
  
  res.end(JSON.stringify({
    status: 'ok',
    message: 'Test backend is working',
    timestamp: new Date().toISOString()
  }));
});

const PORT = 3001;
server.listen(PORT, () => {
  console.log(`Test backend running on http://localhost:${PORT}`);
});

server.on('error', (err) => {
  console.error('Server error:', err);
});
