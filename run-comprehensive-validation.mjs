#!/usr/bin/env node

import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  const timestamp = new Date().toISOString();
  console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
}

function logSection(title) {
  console.log('\n' + '='.repeat(80));
  log(title, 'cyan');
  console.log('='.repeat(80));
}

async function runCommand(command, args, options = {}) {
  return new Promise((resolve, reject) => {
    const process = spawn(command, args, {
      stdio: 'pipe',
      ...options
    });

    let stdout = '';
    let stderr = '';

    process.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    process.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    process.on('close', (code) => {
      if (code === 0) {
        resolve({ stdout, stderr, code });
      } else {
        reject(new Error(`Command failed with code ${code}\nSTDOUT: ${stdout}\nSTDERR: ${stderr}`));
      }
    });
  });
}

async function setupTestEnvironment() {
  logSection('🔧 TEST ENVIRONMENT SETUP');
  
  try {
    // Ensure test directories exist
    const testDirs = [
      'tests/integration',
      'tests/stress',
      'tests/reports'
    ];

    for (const dir of testDirs) {
      try {
        await fs.access(path.join(__dirname, dir));
        log(`✅ Directory exists: ${dir}`, 'green');
      } catch {
        await fs.mkdir(path.join(__dirname, dir), { recursive: true });
        log(`📁 Created directory: ${dir}`, 'blue');
      }
    }

    // Check if Jest is available
    try {
      await runCommand('npx', ['jest', '--version']);
      log('✅ Jest is available', 'green');
    } catch (error) {
      log('❌ Jest not found, installing...', 'yellow');
      await runCommand('npm', ['install', '--save-dev', 'jest', '@types/jest', 'ts-jest']);
      log('✅ Jest installed', 'green');
    }

    // Create Jest configuration if it doesn't exist
    const jestConfigPath = path.join(__dirname, 'jest.config.js');
    try {
      await fs.access(jestConfigPath);
      log('✅ Jest configuration exists', 'green');
    } catch {
      const jestConfig = `
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/tests'],
  testMatch: ['**/*.test.ts'],
  transform: {
    '^.+\\.ts$': 'ts-jest',
  },
  moduleNameMapping: {
    '^(\\.{1,2}/.*)\\.js$': '$1',
  },
  extensionsToTreatAsEsm: ['.ts'],
  globals: {
    'ts-jest': {
      useESM: true,
    },
  },
  testTimeout: 300000, // 5 minutes
  maxWorkers: 1, // Run tests sequentially to avoid conflicts
};
`;
      await fs.writeFile(jestConfigPath, jestConfig);
      log('📝 Created Jest configuration', 'blue');
    }

    log('Test environment setup completed', 'green');
  } catch (error) {
    log(`❌ Test environment setup failed: ${error.message}`, 'red');
    throw error;
  }
}

async function runIntegrationTests() {
  logSection('🔗 INTEGRATION TESTS');
  
  try {
    log('Running comprehensive system integration validation...', 'blue');
    
    const result = await runCommand('npx', [
      'jest',
      'tests/integration/comprehensive-system-validation.test.ts',
      '--verbose',
      '--detectOpenHandles',
      '--forceExit'
    ]);

    log('✅ Integration tests completed successfully', 'green');
    return { success: true, output: result.stdout };
  } catch (error) {
    log(`❌ Integration tests failed: ${error.message}`, 'red');
    return { success: false, error: error.message };
  }
}

async function runMultiChainTests() {
  logSection('🌐 MULTI-CHAIN INTEGRATION TESTS');
  
  try {
    log('Running multi-chain arbitrage integration tests...', 'blue');
    
    const result = await runCommand('npx', [
      'jest',
      'tests/integration/multi-chain-integration.test.ts',
      '--verbose',
      '--detectOpenHandles',
      '--forceExit'
    ]);

    log('✅ Multi-chain tests completed successfully', 'green');
    return { success: true, output: result.stdout };
  } catch (error) {
    log(`❌ Multi-chain tests failed: ${error.message}`, 'red');
    return { success: false, error: error.message };
  }
}

async function runStressTests() {
  logSection('💪 STRESS TESTS');
  
  try {
    log('Running system stress tests...', 'blue');
    log('⚠️  This may take several minutes and will generate high load', 'yellow');
    
    const result = await runCommand('npx', [
      'jest',
      'tests/stress/system-stress-test.ts',
      '--verbose',
      '--detectOpenHandles',
      '--forceExit',
      '--maxWorkers=1'
    ]);

    log('✅ Stress tests completed successfully', 'green');
    return { success: true, output: result.stdout };
  } catch (error) {
    log(`❌ Stress tests failed: ${error.message}`, 'red');
    return { success: false, error: error.message };
  }
}

async function runPerformanceBenchmarks() {
  logSection('📊 PERFORMANCE BENCHMARKS');
  
  try {
    log('Running performance benchmarks...', 'blue');
    
    // Start the enhanced server for benchmarking
    log('Starting enhanced server for benchmarking...', 'blue');
    const serverProcess = spawn('node', ['start-enhanced-system.mjs'], {
      stdio: 'pipe',
      detached: false
    });

    // Wait for server to start
    await new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        serverProcess.kill();
        reject(new Error('Server startup timeout'));
      }, 60000);

      serverProcess.stdout.on('data', (data) => {
        const output = data.toString();
        if (output.includes('Enhanced MEV Arbitrage Bot System is fully operational')) {
          clearTimeout(timeout);
          resolve();
        }
      });

      serverProcess.stderr.on('data', (data) => {
        const output = data.toString();
        if (output.includes('Error') || output.includes('Failed')) {
          clearTimeout(timeout);
          serverProcess.kill();
          reject(new Error('Server startup failed'));
        }
      });
    });

    log('✅ Server started, running benchmarks...', 'green');

    // Run performance tests
    const benchmarkResults = await runPerformanceBenchmarkSuite();

    // Stop the server
    serverProcess.kill('SIGTERM');
    
    log('✅ Performance benchmarks completed', 'green');
    return { success: true, results: benchmarkResults };
  } catch (error) {
    log(`❌ Performance benchmarks failed: ${error.message}`, 'red');
    return { success: false, error: error.message };
  }
}

async function runPerformanceBenchmarkSuite() {
  const benchmarks = [];

  // API Response Time Benchmark
  log('Testing API response times...', 'blue');
  const apiEndpoints = [
    '/health',
    '/api/system/metrics',
    '/api/opportunities',
    '/api/trades',
    '/api/system/integration'
  ];

  for (const endpoint of apiEndpoints) {
    const times = [];
    for (let i = 0; i < 10; i++) {
      const start = Date.now();
      try {
        const response = await fetch(`http://localhost:8080${endpoint}`);
        const latency = Date.now() - start;
        times.push(latency);
        
        if (!response.ok) {
          log(`⚠️  ${endpoint} returned ${response.status}`, 'yellow');
        }
      } catch (error) {
        log(`❌ ${endpoint} failed: ${error.message}`, 'red');
      }
    }
    
    if (times.length > 0) {
      const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
      const maxTime = Math.max(...times);
      const minTime = Math.min(...times);
      
      benchmarks.push({
        endpoint,
        averageMs: avgTime,
        maxMs: maxTime,
        minMs: minTime,
        samples: times.length
      });
      
      const status = avgTime < 500 ? '✅' : avgTime < 1000 ? '⚠️' : '❌';
      log(`${status} ${endpoint}: ${avgTime.toFixed(2)}ms avg (${minTime}-${maxTime}ms)`, 
          avgTime < 500 ? 'green' : avgTime < 1000 ? 'yellow' : 'red');
    }
  }

  // WebSocket Connection Test
  log('Testing WebSocket connection...', 'blue');
  try {
    const wsStart = Date.now();
    const ws = new WebSocket('ws://localhost:8080/ws');
    
    await new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        ws.close();
        reject(new Error('WebSocket timeout'));
      }, 5000);

      ws.onopen = () => {
        clearTimeout(timeout);
        const wsLatency = Date.now() - wsStart;
        benchmarks.push({
          endpoint: 'WebSocket Connection',
          averageMs: wsLatency,
          maxMs: wsLatency,
          minMs: wsLatency,
          samples: 1
        });
        
        log(`✅ WebSocket: ${wsLatency}ms connection time`, 'green');
        ws.close();
        resolve();
      };

      ws.onerror = () => {
        clearTimeout(timeout);
        reject(new Error('WebSocket connection failed'));
      };
    });
  } catch (error) {
    log(`❌ WebSocket test failed: ${error.message}`, 'red');
  }

  return benchmarks;
}

async function generateTestReport(results) {
  logSection('📋 GENERATING TEST REPORT');
  
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      integrationTests: results.integration?.success || false,
      multiChainTests: results.multiChain?.success || false,
      stressTests: results.stress?.success || false,
      performanceBenchmarks: results.performance?.success || false
    },
    details: results,
    systemInfo: {
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      memoryUsage: process.memoryUsage(),
      uptime: process.uptime()
    }
  };

  const reportPath = path.join(__dirname, 'tests/reports', `validation-report-${Date.now()}.json`);
  await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
  
  log(`📄 Test report saved: ${reportPath}`, 'green');
  
  // Generate summary
  const passedTests = Object.values(report.summary).filter(Boolean).length;
  const totalTests = Object.values(report.summary).length;
  
  log(`\n📊 TEST SUMMARY:`, 'cyan');
  log(`   • Integration Tests: ${report.summary.integrationTests ? '✅ PASS' : '❌ FAIL'}`, 
      report.summary.integrationTests ? 'green' : 'red');
  log(`   • Multi-Chain Tests: ${report.summary.multiChainTests ? '✅ PASS' : '❌ FAIL'}`, 
      report.summary.multiChainTests ? 'green' : 'red');
  log(`   • Stress Tests: ${report.summary.stressTests ? '✅ PASS' : '❌ FAIL'}`, 
      report.summary.stressTests ? 'green' : 'red');
  log(`   • Performance Tests: ${report.summary.performanceBenchmarks ? '✅ PASS' : '❌ FAIL'}`, 
      report.summary.performanceBenchmarks ? 'green' : 'red');
  log(`\n🎯 Overall: ${passedTests}/${totalTests} test suites passed`, 
      passedTests === totalTests ? 'green' : 'red');

  return report;
}

async function main() {
  try {
    console.log(`${colors.bright}${colors.magenta}`);
    console.log('╔══════════════════════════════════════════════════════════════════════════════╗');
    console.log('║                    COMPREHENSIVE SYSTEM VALIDATION                          ║');
    console.log('║                     Enhanced MEV Arbitrage Bot                              ║');
    console.log('╚══════════════════════════════════════════════════════════════════════════════╝');
    console.log(colors.reset);

    const startTime = Date.now();
    const results = {};

    // Step 1: Setup test environment
    await setupTestEnvironment();

    // Step 2: Run integration tests
    results.integration = await runIntegrationTests();

    // Step 3: Run multi-chain tests
    results.multiChain = await runMultiChainTests();

    // Step 4: Run stress tests
    results.stress = await runStressTests();

    // Step 5: Run performance benchmarks
    results.performance = await runPerformanceBenchmarks();

    // Step 6: Generate comprehensive report
    const report = await generateTestReport(results);

    const totalTime = Date.now() - startTime;
    
    logSection('🎉 VALIDATION COMPLETE');
    log(`Total validation time: ${(totalTime / 1000 / 60).toFixed(2)} minutes`, 'green');
    
    const allPassed = Object.values(report.summary).every(Boolean);
    if (allPassed) {
      log('🎉 ALL VALIDATION TESTS PASSED! System is ready for production.', 'green');
      process.exit(0);
    } else {
      log('⚠️  Some validation tests failed. Review the report for details.', 'yellow');
      process.exit(1);
    }

  } catch (error) {
    log(`❌ Validation failed: ${error.message}`, 'red');
    console.error(error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  log('\n🛑 Validation interrupted by user', 'yellow');
  process.exit(1);
});

process.on('SIGTERM', () => {
  log('\n🛑 Validation terminated', 'yellow');
  process.exit(1);
});

// Run the validation
main();
