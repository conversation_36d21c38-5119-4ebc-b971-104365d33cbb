# MEV Arbitrage Bot

A sophisticated MEV (Maximal Extractable Value) arbitrage bot with a simplified HTML frontend and Node.js backend. This bot automatically detects and executes profitable arbitrage opportunities across multiple DEXs while managing risk and optimizing gas costs.

## ✅ **WORKING SYSTEM - SIMPLIFIED ARCHITECTURE**

This system has been streamlined to use:

- **Frontend**: Pure HTML/CSS/JavaScript (no React/Vite complexity)
- **Backend**: Node.js with Express (working-backend.mjs)
- **Real-time Data**: Live API integration with 30-second refresh
- **Guaranteed Functionality**: No build steps, no dependencies issues

## 🚀 **QUICK START**

### Option 1: Simple Startup (Recommended)
```bash
node start.mjs
```
Then open `index.html` in your browser.

### Option 2: Manual Startup
```bash
# Start backend
node working-backend.mjs

# Open frontend
# Open index.html in your browser
```

### What You'll See:
- ✅ **Backend Status**: Connection indicator with real-time status
- 📊 **KPIs**: Total Profit ($2,650.50), Win Rate (86.7%), Total Trades (45), Daily Volume ($125,000)
- 🔍 **Live Opportunities**: 3 arbitrage opportunities with profit calculations
- 💰 **Recent Trades**: 3 executed trades with profit/loss data
- 🪙 **Monitored Tokens**: ETH, USDC, WBTC with safety scores
- 🐛 **Debug Information**: Real-time logs and connection status

## Features

### Smart Contracts

- **TokenDiscovery.sol**: Manages token whitelists and blacklists with safety scoring
- **LiquidityChecker.sol**: Monitors liquidity across DEX pools
- **ArbitrageExecutor.sol**: Executes arbitrage trades with flash loans
- **Governance.sol**: Manages bot parameters and emergency controls

### Backend Services

- **Token Discovery Service**: Automatically discovers and validates new tokens
- **Price Feed Service**: Real-time price aggregation from multiple DEXs
- **Opportunity Detection Service**: Identifies arbitrage opportunities across chains
- **Execution Service**: Executes profitable trades with risk management
- **Risk Management Service**: Monitors and controls trading risks
- **Analytics Service**: Tracks performance and generates insights

### Frontend Dashboard

- Real-time opportunity monitoring
- Trade execution tracking
- Risk management controls
- System health monitoring
- Token management interface
- Analytics and reporting

## Quick Start

**Prerequisites:** Node.js 18+, Redis

1. **Install dependencies:**

   ```bash
   npm install
   ```

2. **Set up environment:**

   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start Redis:**

   ```bash
   # Using Docker
   docker run -d -p 6379:6379 redis:alpine
   ```

4. **Run the application:**

   ```bash
   # Start both frontend and backend
   npm run dev

   # Or separately:
   npm run dev:frontend  # Frontend on http://localhost:5173
   npm run dev:backend   # Backend on http://localhost:3001
   ```

5. **Deploy smart contracts (optional):**

   ```bash
   npm run compile:contracts
   npm run deploy:contracts
   ```

## Architecture

The system consists of three main layers:

1. **Frontend (React)**: Dashboard for monitoring and control
2. **Backend (Node.js)**: Services for opportunity detection and execution
3. **Smart Contracts (Solidity)**: On-chain arbitrage execution

## Key Features

- **Real-time Monitoring**: Live opportunity detection and trade tracking
- **Multi-chain Support**: Ethereum, Polygon, BSC, Solana
- **Risk Management**: Comprehensive risk controls and emergency stops
- **Analytics**: Performance tracking and insights
- **WebSocket Integration**: Real-time updates across the system

## Configuration

Key environment variables:

- `ETHEREUM_RPC_URL`: Ethereum RPC endpoint
- `MIN_PROFIT_THRESHOLD`: Minimum profit in USD (default: 50)
- `MAX_POSITION_SIZE`: Maximum position size in USD (default: 10000)
- `EMERGENCY_STOP`: Emergency stop flag (default: false)

## API Endpoints

- `GET /api/opportunities` - Get arbitrage opportunities
- `GET /api/trades` - Get trade history
- `GET /api/tokens` - Get token whitelist
- `GET /api/system/health` - Get system health
- `GET /api/analytics/performance` - Get performance metrics

## Security

⚠️ **Important**: This is for educational purposes. Never use real private keys in development. Always test with small amounts first.

## License

MIT License - see LICENSE file for details.
