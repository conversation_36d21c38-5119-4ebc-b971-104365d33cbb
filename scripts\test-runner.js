#!/usr/bin/env node

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs/promises';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = join(__dirname, '..');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

class TestRunner {
  constructor() {
    this.results = {
      unit: null,
      integration: null,
      performance: null,
      contracts: null,
      e2e: null
    };
    this.startTime = Date.now();
  }

  log(message, color = colors.reset) {
    console.log(`${color}${message}${colors.reset}`);
  }

  async runCommand(command, args, options = {}) {
    return new Promise((resolve, reject) => {
      const child = spawn(command, args, {
        stdio: 'inherit',
        cwd: rootDir,
        ...options
      });

      child.on('close', (code) => {
        if (code === 0) {
          resolve(code);
        } else {
          reject(new Error(`Command failed with exit code ${code}`));
        }
      });

      child.on('error', reject);
    });
  }

  async checkPrerequisites() {
    this.log('\n🔍 Checking prerequisites...', colors.cyan);
    
    try {
      // Check if node_modules exists
      await fs.access(join(rootDir, 'node_modules'));
      this.log('✅ Dependencies installed', colors.green);
    } catch {
      this.log('❌ Dependencies not installed. Run: npm install', colors.red);
      process.exit(1);
    }

    // Check if Redis is available (for integration tests)
    try {
      await this.runCommand('redis-cli', ['ping'], { stdio: 'pipe' });
      this.log('✅ Redis is available', colors.green);
    } catch {
      this.log('⚠️  Redis not available - some integration tests may fail', colors.yellow);
    }
  }

  async runUnitTests() {
    this.log('\n🧪 Running Unit Tests...', colors.bright);
    
    try {
      await this.runCommand('npm', ['run', 'test', '--', '--testPathPattern=tests/unit']);
      this.results.unit = { status: 'passed', duration: Date.now() - this.startTime };
      this.log('✅ Unit tests passed', colors.green);
    } catch (error) {
      this.results.unit = { status: 'failed', error: error.message };
      this.log('❌ Unit tests failed', colors.red);
      throw error;
    }
  }

  async runIntegrationTests() {
    this.log('\n🔗 Running Integration Tests...', colors.bright);
    
    try {
      await this.runCommand('npm', ['run', 'test:integration']);
      this.results.integration = { status: 'passed', duration: Date.now() - this.startTime };
      this.log('✅ Integration tests passed', colors.green);
    } catch (error) {
      this.results.integration = { status: 'failed', error: error.message };
      this.log('❌ Integration tests failed', colors.red);
      throw error;
    }
  }

  async runPerformanceTests() {
    this.log('\n⚡ Running Performance Tests...', colors.bright);
    
    try {
      // Run Jest performance tests
      await this.runCommand('npm', ['run', 'test:performance']);
      
      // Run custom benchmark
      await this.runCommand('npm', ['run', 'benchmark']);
      
      this.results.performance = { status: 'passed', duration: Date.now() - this.startTime };
      this.log('✅ Performance tests passed', colors.green);
    } catch (error) {
      this.results.performance = { status: 'failed', error: error.message };
      this.log('❌ Performance tests failed', colors.red);
      throw error;
    }
  }

  async runContractTests() {
    this.log('\n📜 Running Smart Contract Tests...', colors.bright);
    
    try {
      // Compile contracts first
      await this.runCommand('npm', ['run', 'compile:contracts']);
      
      // Run contract tests
      await this.runCommand('npm', ['run', 'test:contracts']);
      
      this.results.contracts = { status: 'passed', duration: Date.now() - this.startTime };
      this.log('✅ Contract tests passed', colors.green);
    } catch (error) {
      this.results.contracts = { status: 'failed', error: error.message };
      this.log('❌ Contract tests failed', colors.red);
      throw error;
    }
  }

  async runE2ETests() {
    this.log('\n🌐 Running End-to-End Tests...', colors.bright);
    
    try {
      await this.runCommand('npm', ['run', 'test:e2e']);
      this.results.e2e = { status: 'passed', duration: Date.now() - this.startTime };
      this.log('✅ E2E tests passed', colors.green);
    } catch (error) {
      this.results.e2e = { status: 'failed', error: error.message };
      this.log('❌ E2E tests failed', colors.red);
      throw error;
    }
  }

  async generateCoverageReport() {
    this.log('\n📊 Generating Coverage Report...', colors.cyan);
    
    try {
      await this.runCommand('npm', ['run', 'test:coverage']);
      this.log('✅ Coverage report generated', colors.green);
    } catch (error) {
      this.log('⚠️  Coverage report generation failed', colors.yellow);
    }
  }

  printSummary() {
    const totalDuration = Date.now() - this.startTime;
    
    this.log('\n' + '='.repeat(60), colors.magenta);
    this.log('📋 TEST SUMMARY REPORT', colors.bright);
    this.log('='.repeat(60), colors.magenta);
    
    Object.entries(this.results).forEach(([testType, result]) => {
      if (result) {
        const status = result.status === 'passed' ? '✅' : '❌';
        const statusColor = result.status === 'passed' ? colors.green : colors.red;
        this.log(`${status} ${testType.toUpperCase()}: ${result.status}`, statusColor);
        
        if (result.error) {
          this.log(`   Error: ${result.error}`, colors.red);
        }
      } else {
        this.log(`⏭️  ${testType.toUpperCase()}: skipped`, colors.yellow);
      }
    });
    
    this.log('\n' + '='.repeat(60), colors.magenta);
    this.log(`⏱️  Total Duration: ${(totalDuration / 1000).toFixed(2)}s`, colors.cyan);
    
    const passedTests = Object.values(this.results).filter(r => r?.status === 'passed').length;
    const totalTests = Object.values(this.results).filter(r => r !== null).length;
    
    this.log(`📈 Success Rate: ${passedTests}/${totalTests} (${((passedTests/totalTests)*100).toFixed(1)}%)`, colors.cyan);
    
    if (passedTests === totalTests) {
      this.log('\n🎉 ALL TESTS PASSED! System is ready for deployment.', colors.green);
    } else {
      this.log('\n⚠️  Some tests failed. Please review and fix issues before deployment.', colors.red);
    }
  }

  async runAllTests() {
    this.log('🚀 MEV Arbitrage Bot - Comprehensive Test Suite', colors.bright);
    this.log('Starting full system testing...', colors.cyan);
    
    try {
      await this.checkPrerequisites();
      
      // Run tests in order of complexity
      await this.runUnitTests();
      await this.runContractTests();
      await this.runIntegrationTests();
      await this.runPerformanceTests();
      await this.runE2ETests();
      
      // Generate coverage report
      await this.generateCoverageReport();
      
    } catch (error) {
      this.log(`\n💥 Test suite failed: ${error.message}`, colors.red);
    } finally {
      this.printSummary();
    }
  }

  async runSpecificTest(testType) {
    this.log(`🎯 Running ${testType} tests only...`, colors.cyan);
    
    try {
      await this.checkPrerequisites();
      
      switch (testType) {
        case 'unit':
          await this.runUnitTests();
          break;
        case 'integration':
          await this.runIntegrationTests();
          break;
        case 'performance':
          await this.runPerformanceTests();
          break;
        case 'contracts':
          await this.runContractTests();
          break;
        case 'e2e':
          await this.runE2ETests();
          break;
        default:
          throw new Error(`Unknown test type: ${testType}`);
      }
      
    } catch (error) {
      this.log(`💥 ${testType} tests failed: ${error.message}`, colors.red);
    } finally {
      this.printSummary();
    }
  }
}

// CLI interface
const args = process.argv.slice(2);
const testRunner = new TestRunner();

if (args.length === 0) {
  // Run all tests
  testRunner.runAllTests().catch(console.error);
} else {
  // Run specific test type
  const testType = args[0];
  testRunner.runSpecificTest(testType).catch(console.error);
}

export default TestRunner;
