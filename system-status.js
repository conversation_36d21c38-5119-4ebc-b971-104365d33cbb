// MEV Arbitrage Bot System Status Check
import { createClient } from '@supabase/supabase-js';
import { InfluxDB } from '@influxdata/influxdb-client';
import dotenv from 'dotenv';

dotenv.config();

console.log('🚀 MEV Arbitrage Bot - System Status Check\n');

async function checkSystemStatus() {
  const status = {
    frontend: false,
    backend: false,
    supabase: false,
    influxdb: false,
    docker: false
  };

  // Check Frontend (Vite Dev Server)
  console.log('🎨 Checking Frontend...');
  try {
    const frontendResponse = await fetch('http://localhost:5173');
    if (frontendResponse.ok) {
      status.frontend = true;
      console.log('✅ Frontend: Running on http://localhost:5173');
    } else {
      console.log('❌ Frontend: Not responding');
    }
  } catch (error) {
    console.log('❌ Frontend: Not running');
  }

  // Check Backend API
  console.log('\n🔧 Checking Backend...');
  try {
    const backendResponse = await fetch('http://localhost:3001/health');
    if (backendResponse.ok) {
      const healthData = await backendResponse.json();
      status.backend = true;
      console.log('✅ Backend: Running on http://localhost:3001');
      console.log(`   Status: ${healthData.status}`);
      console.log(`   Services: ${Object.keys(healthData.services).length} active`);
      
      // Test API endpoints
      const endpoints = [
        '/api/opportunities',
        '/api/tokens',
        '/api/analytics/performance'
      ];
      
      for (const endpoint of endpoints) {
        try {
          const response = await fetch(`http://localhost:3001${endpoint}`);
          const data = await response.json();
          if (response.ok && data.success) {
            console.log(`   ✅ ${endpoint}: ${data.data?.length || 0} records`);
          } else {
            console.log(`   ⚠️  ${endpoint}: ${data.message || 'No data'}`);
          }
        } catch (error) {
          console.log(`   ❌ ${endpoint}: Error`);
        }
      }
    } else {
      console.log('❌ Backend: Not responding properly');
    }
  } catch (error) {
    console.log('❌ Backend: Not running');
  }

  // Check Supabase
  console.log('\n📊 Checking Supabase...');
  try {
    const supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      { auth: { autoRefreshToken: false, persistSession: false } }
    );

    const { data, error } = await supabase
      .from('configuration')
      .select('key')
      .limit(1);

    if (!error) {
      status.supabase = true;
      console.log('✅ Supabase: Connected successfully');
      console.log(`   URL: ${process.env.SUPABASE_URL}`);
      
      // Check table counts
      const tables = ['trades', 'opportunities', 'performance_metrics', 'tokens', 'system_alerts', 'configuration'];
      for (const table of tables) {
        try {
          const { count, error: countError } = await supabase
            .from(table)
            .select('*', { count: 'exact', head: true });
          
          if (!countError) {
            console.log(`   📋 ${table}: ${count || 0} records`);
          }
        } catch (e) {
          console.log(`   ⚠️  ${table}: Error checking`);
        }
      }
    } else {
      console.log('❌ Supabase: Connection failed');
    }
  } catch (error) {
    console.log('❌ Supabase: Error -', error.message);
  }

  // Check InfluxDB
  console.log('\n📈 Checking InfluxDB...');
  try {
    const influxDB = new InfluxDB({
      url: process.env.INFLUXDB_URL,
      token: process.env.INFLUXDB_TOKEN,
    });

    const queryApi = influxDB.getQueryApi(process.env.INFLUXDB_ORG);
    const query = `
      from(bucket: "${process.env.INFLUXDB_BUCKET}")
        |> range(start: -1h)
        |> limit(n: 1)
    `;

    let hasData = false;
    await queryApi.queryRows(query, {
      next() { hasData = true; },
      error(error) { console.log('   Query error:', error.message); },
      complete() {}
    });

    status.influxdb = true;
    console.log('✅ InfluxDB: Connected successfully');
    console.log(`   URL: ${process.env.INFLUXDB_URL}`);
    console.log(`   Org: ${process.env.INFLUXDB_ORG}`);
    console.log(`   Bucket: ${process.env.INFLUXDB_BUCKET}`);
    console.log(`   Data: ${hasData ? 'Available' : 'No recent data'}`);

  } catch (error) {
    console.log('❌ InfluxDB: Error -', error.message);
  }

  // Check Docker containers
  console.log('\n🐳 Checking Docker...');
  try {
    const { exec } = await import('child_process');
    const { promisify } = await import('util');
    const execAsync = promisify(exec);
    
    const { stdout } = await execAsync('docker ps --format "table {{.Names}}\\t{{.Status}}\\t{{.Ports}}"');
    const lines = stdout.trim().split('\n');
    
    if (lines.length > 1) {
      status.docker = true;
      console.log('✅ Docker: Containers running');
      lines.forEach((line, index) => {
        if (index === 0) {
          console.log(`   ${line}`);
        } else {
          console.log(`   ${line}`);
        }
      });
    } else {
      console.log('⚠️  Docker: No containers running');
    }
  } catch (error) {
    console.log('❌ Docker: Error checking containers');
  }

  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('📋 SYSTEM STATUS SUMMARY');
  console.log('='.repeat(60));
  console.log(`Frontend:  ${status.frontend ? '✅ RUNNING' : '❌ STOPPED'} (http://localhost:5173)`);
  console.log(`Backend:   ${status.backend ? '✅ RUNNING' : '❌ STOPPED'} (http://localhost:3001)`);
  console.log(`Supabase:  ${status.supabase ? '✅ CONNECTED' : '❌ DISCONNECTED'}`);
  console.log(`InfluxDB:  ${status.influxdb ? '✅ CONNECTED' : '❌ DISCONNECTED'}`);
  console.log(`Docker:    ${status.docker ? '✅ RUNNING' : '❌ STOPPED'}`);

  const totalServices = Object.keys(status).length;
  const runningServices = Object.values(status).filter(Boolean).length;
  
  console.log(`\nOverall: ${runningServices}/${totalServices} services operational`);
  
  if (runningServices === totalServices) {
    console.log('\n🎉 ALL SYSTEMS OPERATIONAL!');
    console.log('🚀 Your MEV Arbitrage Bot is fully running!');
    console.log('\n🌐 Access Points:');
    console.log('   • Frontend Dashboard: http://localhost:5173');
    console.log('   • Backend API: http://localhost:3001');
    console.log('   • Health Check: http://localhost:3001/health');
    console.log('   • InfluxDB UI: http://localhost:8086');
  } else {
    console.log('\n⚠️  Some services need attention.');
    console.log('📚 Check the status above for troubleshooting.');
  }
}

checkSystemStatus().catch(console.error);
