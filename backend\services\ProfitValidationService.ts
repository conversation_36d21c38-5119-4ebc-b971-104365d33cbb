import { EventEmitter } from 'events';
import logger from '../utils/logger.js';
import config from '../config/index.js';
import { ArbitrageOpportunity } from './OpportunityDetectionService.js';
import { Trade, TradeStatus } from './ExecutionService.js';
import { ValidationResult } from './PreExecutionValidationService.js';

export interface ProfitValidationResult {
  isValid: boolean;
  reason?: string;
  predictedProfit: number;
  actualProfit: number;
  profitDifference: number;
  profitAccuracy: number; // percentage
  validationPassed: boolean;
  postExecutionPassed: boolean;
}

export interface ProfitMetrics {
  totalTrades: number;
  profitableTrades: number;
  unprofitableTrades: number;
  totalProfit: number;
  averageProfit: number;
  profitAccuracy: number;
  predictionAccuracy: number;
  rejectedTrades: number;
  rejectionReasons: Map<string, number>;
}

export interface ProfitThresholds {
  minAbsoluteProfit: number; // Minimum profit in USD
  minProfitMargin: number; // Minimum profit margin percentage
  maxAcceptableLoss: number; // Maximum acceptable loss in USD
  profitAccuracyThreshold: number; // Minimum prediction accuracy percentage
}

export class ProfitValidationService extends EventEmitter {
  private isRunning = false;
  private profitMetrics: ProfitMetrics;
  private profitThresholds: ProfitThresholds;
  private tradeHistory: Map<string, ProfitValidationResult> = new Map();
  
  // Profit validation parameters
  private readonly strictProfitValidation = true;
  private readonly enablePostExecutionValidation = true;
  private readonly profitPredictionWindow = 30; // seconds
  
  constructor() {
    super();
    
    this.profitThresholds = {
      minAbsoluteProfit: parseFloat(config.MIN_PROFIT_THRESHOLD || '10'),
      minProfitMargin: parseFloat(config.MIN_PROFIT_MARGIN_BUFFER || '5'),
      maxAcceptableLoss: 0, // No acceptable loss - strict profit validation
      profitAccuracyThreshold: parseFloat(config.PROFIT_ACCURACY_THRESHOLD || '80')
    };
    
    this.profitMetrics = {
      totalTrades: 0,
      profitableTrades: 0,
      unprofitableTrades: 0,
      totalProfit: 0,
      averageProfit: 0,
      profitAccuracy: 0,
      predictionAccuracy: 0,
      rejectedTrades: 0,
      rejectionReasons: new Map()
    };
  }

  public async start() {
    if (this.isRunning) return;
    
    logger.info('Starting Profit Validation Service...');
    this.isRunning = true;
    
    logger.info('Profit Validation Service started with strict validation enabled');
  }

  public async stop() {
    if (!this.isRunning) return;
    
    logger.info('Stopping Profit Validation Service...');
    this.isRunning = false;
    logger.info('Profit Validation Service stopped');
  }

  /**
   * Validate opportunity profitability before execution
   */
  public async validatePreExecution(
    opportunity: ArbitrageOpportunity,
    validationResult?: ValidationResult
  ): Promise<ProfitValidationResult> {
    
    try {
      logger.debug(`Validating pre-execution profit for opportunity ${opportunity.id}`);

      // Use validation result if available, otherwise use opportunity data
      const predictedProfit = validationResult?.simulatedProfit || opportunity.potentialProfit;
      const totalCosts = validationResult?.totalCosts?.totalCosts || 0;
      const netProfit = predictedProfit - totalCosts;

      // Strict profit validation checks
      const validationChecks = await this.performProfitValidationChecks(
        opportunity,
        netProfit,
        validationResult
      );

      const isValid = validationChecks.every(check => check.passed);
      const failedChecks = validationChecks.filter(check => !check.passed);
      
      const result: ProfitValidationResult = {
        isValid,
        reason: isValid ? undefined : failedChecks.map(c => c.reason).join('; '),
        predictedProfit: netProfit,
        actualProfit: 0, // Will be set post-execution
        profitDifference: 0,
        profitAccuracy: 0,
        validationPassed: isValid,
        postExecutionPassed: false // Will be set post-execution
      };

      // Track validation result
      this.tradeHistory.set(opportunity.id, result);

      // Update metrics
      if (!isValid) {
        this.profitMetrics.rejectedTrades++;
        failedChecks.forEach(check => {
          const count = this.profitMetrics.rejectionReasons.get(check.reason) || 0;
          this.profitMetrics.rejectionReasons.set(check.reason, count + 1);
        });
      }

      // Emit validation event
      this.emit('preExecutionValidation', {
        opportunity,
        result,
        validationChecks
      });

      logger.info(`Pre-execution validation for ${opportunity.id}: ${isValid ? 'PASSED' : 'FAILED'}`);
      if (!isValid) {
        logger.info(`  Rejection reason: ${result.reason}`);
      }

      return result;

    } catch (error) {
      logger.error(`Pre-execution validation failed for ${opportunity.id}:`, error);
      
      return {
        isValid: false,
        reason: `Validation error: ${error instanceof Error ? error.message : String(error)}`,
        predictedProfit: 0,
        actualProfit: 0,
        profitDifference: 0,
        profitAccuracy: 0,
        validationPassed: false,
        postExecutionPassed: false
      };
    }
  }

  /**
   * Validate actual profit after trade execution
   */
  public async validatePostExecution(
    opportunity: ArbitrageOpportunity,
    trade: Trade
  ): Promise<ProfitValidationResult> {
    
    try {
      logger.debug(`Validating post-execution profit for trade ${trade.id}`);

      const preValidationResult = this.tradeHistory.get(opportunity.id);
      if (!preValidationResult) {
        throw new Error('No pre-execution validation result found');
      }

      const actualProfit = trade.executedProfit;
      const predictedProfit = preValidationResult.predictedProfit;
      const profitDifference = actualProfit - predictedProfit;
      const profitAccuracy = predictedProfit > 0 ? 
        Math.max(0, 100 - Math.abs(profitDifference / predictedProfit) * 100) : 0;

      // Post-execution validation checks
      const postExecutionPassed = this.validatePostExecutionProfit(actualProfit, trade);

      const result: ProfitValidationResult = {
        ...preValidationResult,
        actualProfit,
        profitDifference,
        profitAccuracy,
        postExecutionPassed
      };

      // Update trade history
      this.tradeHistory.set(opportunity.id, result);

      // Update profit metrics
      this.updateProfitMetrics(result, trade);

      // Emit post-execution validation event
      this.emit('postExecutionValidation', {
        opportunity,
        trade,
        result
      });

      logger.info(`Post-execution validation for ${trade.id}: ${postExecutionPassed ? 'PASSED' : 'FAILED'}`);
      logger.info(`  Predicted profit: $${predictedProfit.toFixed(2)}`);
      logger.info(`  Actual profit: $${actualProfit.toFixed(2)}`);
      logger.info(`  Profit accuracy: ${profitAccuracy.toFixed(2)}%`);

      return result;

    } catch (error) {
      logger.error(`Post-execution validation failed for ${opportunity.id}:`, error);
      
      const preResult = this.tradeHistory.get(opportunity.id);
      return {
        ...preResult,
        actualProfit: trade.executedProfit,
        profitDifference: 0,
        profitAccuracy: 0,
        postExecutionPassed: false
      } as ProfitValidationResult;
    }
  }

  /**
   * Perform comprehensive profit validation checks
   */
  private async performProfitValidationChecks(
    opportunity: ArbitrageOpportunity,
    netProfit: number,
    validationResult?: ValidationResult
  ): Promise<Array<{check: string, passed: boolean, reason: string}>> {
    
    const checks = [];

    // Check 1: Minimum absolute profit
    checks.push({
      check: 'minAbsoluteProfit',
      passed: netProfit >= this.profitThresholds.minAbsoluteProfit,
      reason: `Net profit $${netProfit.toFixed(2)} below minimum $${this.profitThresholds.minAbsoluteProfit}`
    });

    // Check 2: Minimum profit margin
    const profitMargin = validationResult?.profitMargin || 
      (netProfit / opportunity.potentialProfit) * 100;
    checks.push({
      check: 'minProfitMargin',
      passed: profitMargin >= this.profitThresholds.minProfitMargin,
      reason: `Profit margin ${profitMargin.toFixed(2)}% below minimum ${this.profitThresholds.minProfitMargin}%`
    });

    // Check 3: No acceptable loss
    checks.push({
      check: 'noLoss',
      passed: netProfit > -this.profitThresholds.maxAcceptableLoss,
      reason: `Potential loss $${Math.abs(netProfit).toFixed(2)} exceeds maximum acceptable loss $${this.profitThresholds.maxAcceptableLoss}`
    });

    // Check 4: Risk-adjusted profit validation
    const riskScore = validationResult?.riskScore || 50;
    const riskAdjustedMinProfit = this.profitThresholds.minAbsoluteProfit * (1 + riskScore / 100);
    checks.push({
      check: 'riskAdjustedProfit',
      passed: netProfit >= riskAdjustedMinProfit,
      reason: `Risk-adjusted profit requirement $${riskAdjustedMinProfit.toFixed(2)} not met (risk score: ${riskScore})`
    });

    // Check 5: Flash loan profitability (if applicable)
    if (validationResult?.flashLoanOptimization) {
      const flashLoanProfit = validationResult.flashLoanOptimization.expectedProfit;
      checks.push({
        check: 'flashLoanProfitability',
        passed: flashLoanProfit > 0,
        reason: `Flash loan strategy shows negative profit: $${flashLoanProfit.toFixed(2)}`
      });
    }

    return checks;
  }

  /**
   * Validate post-execution profit
   */
  private validatePostExecutionProfit(actualProfit: number, trade: Trade): boolean {
    // Strict validation: actual profit must be positive
    if (actualProfit <= 0) {
      logger.warn(`Trade ${trade.id} resulted in loss: $${actualProfit.toFixed(2)}`);
      return false;
    }

    // Additional checks can be added here
    return true;
  }

  /**
   * Update profit metrics
   */
  private updateProfitMetrics(result: ProfitValidationResult, trade: Trade) {
    this.profitMetrics.totalTrades++;
    
    if (result.actualProfit > 0) {
      this.profitMetrics.profitableTrades++;
    } else {
      this.profitMetrics.unprofitableTrades++;
    }
    
    this.profitMetrics.totalProfit += result.actualProfit;
    this.profitMetrics.averageProfit = this.profitMetrics.totalProfit / this.profitMetrics.totalTrades;
    
    // Update prediction accuracy
    const accuracySum = Array.from(this.tradeHistory.values())
      .reduce((sum, r) => sum + r.profitAccuracy, 0);
    this.profitMetrics.predictionAccuracy = accuracySum / this.tradeHistory.size;
    
    // Update profit accuracy (percentage of profitable trades)
    this.profitMetrics.profitAccuracy = 
      (this.profitMetrics.profitableTrades / this.profitMetrics.totalTrades) * 100;
  }

  /**
   * Get profit validation statistics
   */
  public getProfitValidationStats() {
    return {
      isRunning: this.isRunning,
      profitMetrics: { ...this.profitMetrics },
      profitThresholds: { ...this.profitThresholds },
      totalValidations: this.tradeHistory.size,
      strictValidationEnabled: this.strictProfitValidation,
      postExecutionValidationEnabled: this.enablePostExecutionValidation
    };
  }

  /**
   * Get detailed profit analysis
   */
  public getProfitAnalysis() {
    const validationResults = Array.from(this.tradeHistory.values());
    
    return {
      totalValidations: validationResults.length,
      preExecutionPassRate: validationResults.filter(r => r.validationPassed).length / validationResults.length * 100,
      postExecutionPassRate: validationResults.filter(r => r.postExecutionPassed).length / validationResults.length * 100,
      averageProfitAccuracy: validationResults.reduce((sum, r) => sum + r.profitAccuracy, 0) / validationResults.length,
      profitDistribution: this.calculateProfitDistribution(validationResults),
      rejectionReasons: Object.fromEntries(this.profitMetrics.rejectionReasons)
    };
  }

  /**
   * Calculate profit distribution
   */
  private calculateProfitDistribution(results: ProfitValidationResult[]) {
    const profitable = results.filter(r => r.actualProfit > 0).length;
    const breakeven = results.filter(r => r.actualProfit === 0).length;
    const unprofitable = results.filter(r => r.actualProfit < 0).length;
    
    return {
      profitable: (profitable / results.length) * 100,
      breakeven: (breakeven / results.length) * 100,
      unprofitable: (unprofitable / results.length) * 100
    };
  }

  /**
   * Check if opportunity should be executed based on profit validation
   */
  public shouldExecuteOpportunity(opportunity: ArbitrageOpportunity, validationResult?: ValidationResult): boolean {
    if (!this.strictProfitValidation) return true;
    
    // This would typically be called after validatePreExecution
    const result = this.tradeHistory.get(opportunity.id);
    return result ? result.isValid : false;
  }
}
