<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MEV Arbitrage Bot - Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background: #1F2937;
            color: #D1D5DB;
            font-family: system-ui, -apple-system, sans-serif;
        }
        .card {
            background: #374151;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin: 1rem 0;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .status-success { background: #10B981; color: #065F46; }
        .status-error { background: #EF4444; color: #991B1B; }
        .status-warning { background: #FBBF24; color: #92400E; }
        .status-info { background: #3B82F6; color: #1E3A8A; }
        .grid { display: grid; gap: 1rem; }
        .grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
        .grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
        .grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
        .text-center { text-align: center; }
        .text-3xl { font-size: 1.875rem; }
        .text-xl { font-size: 1.25rem; }
        .text-lg { font-size: 1.125rem; }
        .text-sm { font-size: 0.875rem; }
        .text-xs { font-size: 0.75rem; }
        .font-bold { font-weight: 700; }
        .font-medium { font-weight: 500; }
        .mb-2 { margin-bottom: 0.5rem; }
        .mb-4 { margin-bottom: 1rem; }
        .mb-6 { margin-bottom: 1.5rem; }
        .p-3 { padding: 0.75rem; }
        .p-6 { padding: 1.5rem; }
        .px-4 { padding-left: 1rem; padding-right: 1rem; }
        .py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
        .rounded-lg { border-radius: 0.5rem; }
        .rounded-md { border-radius: 0.375rem; }
        .max-h-96 { max-height: 24rem; }
        .overflow-y-auto { overflow-y: auto; }
        .text-green-400 { color: #34D399; }
        .text-blue-400 { color: #60A5FA; }
        .text-yellow-400 { color: #FBBF24; }
        .text-purple-400 { color: #A78BFA; }
        .text-gray-400 { color: #9CA3AF; }
        .text-gray-300 { color: #D1D5DB; }
        .bg-gray-600 { background-color: #4B5563; }
        .bg-gray-500 { background-color: #6B7280; }
        .hover\:bg-gray-500:hover { background-color: #6B7280; }
        .transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; }
        .flex { display: flex; }
        .justify-between { justify-content: space-between; }
        .items-center { align-items: center; }
        .min-h-screen { min-height: 100vh; }
        .max-w-7xl { max-width: 80rem; }
        .mx-auto { margin-left: auto; margin-right: auto; }
    </style>
</head>
<body>
    <div class="min-h-screen p-6">
        <div class="max-w-7xl mx-auto">
            <h1 class="text-3xl font-bold mb-6">MEV Arbitrage Bot Dashboard</h1>

            <div id="status" class="px-4 py-2 rounded-lg mb-4 status-info">
                Loading backend data...
            </div>

            <!-- KPIs -->
            <div class="grid grid-cols-4 mb-6">
                <div class="card text-center">
                    <h3 class="text-sm font-medium text-gray-400 mb-2">TOTAL PROFIT</h3>
                    <p class="text-3xl font-bold text-green-400" id="total-profit">$0.00</p>
                </div>
                <div class="card text-center">
                    <h3 class="text-sm font-medium text-gray-400 mb-2">WIN RATE</h3>
                    <p class="text-3xl font-bold text-blue-400" id="win-rate">0.0%</p>
                </div>
                <div class="card text-center">
                    <h3 class="text-sm font-medium text-gray-400 mb-2">TOTAL TRADES</h3>
                    <p class="text-3xl font-bold text-yellow-400" id="total-trades">0</p>
                </div>
                <div class="card text-center">
                    <h3 class="text-sm font-medium text-gray-400 mb-2">DAILY VOLUME</h3>
                    <p class="text-3xl font-bold text-purple-400" id="daily-volume">$0</p>
                </div>
            </div>

            <!-- ML Learning Status -->
            <div class="grid grid-cols-4 mb-6">
                <div class="card text-center">
                    <h3 class="text-sm font-medium text-gray-400 mb-2">MARKET REGIME</h3>
                    <p class="text-xl font-bold text-purple-400" id="market-regime">Normal</p>
                </div>
                <div class="card text-center">
                    <h3 class="text-sm font-medium text-gray-400 mb-2">ACTIVE STRATEGIES</h3>
                    <p class="text-xl font-bold text-blue-400" id="active-strategies">0</p>
                </div>
                <div class="card text-center">
                    <h3 class="text-sm font-medium text-gray-400 mb-2">LEARNING EVENTS</h3>
                    <p class="text-xl font-bold text-green-400" id="learning-events">0</p>
                </div>
                <div class="card text-center">
                    <h3 class="text-sm font-medium text-gray-400 mb-2">ADAPTATION RATE</h3>
                    <p class="text-xl font-bold text-yellow-400" id="adaptation-rate">0.0/hr</p>
                </div>
            </div>

            <!-- Data Sections -->
            <div class="grid grid-cols-4">
                <!-- Opportunities -->
                <div class="card">
                    <h2 class="text-xl font-bold mb-4" id="opportunities-title">
                        Live Opportunities (0)
                    </h2>
                    <div class="max-h-96 overflow-y-auto" id="opportunities-list">
                        <p class="text-gray-400 text-center py-8">Loading...</p>
                    </div>
                </div>

                <!-- Recent Trades -->
                <div class="card">
                    <h2 class="text-xl font-bold mb-4" id="trades-title">
                        Recent Trades (0)
                    </h2>
                    <div class="max-h-96 overflow-y-auto" id="trades-list">
                        <p class="text-gray-400 text-center py-8">Loading...</p>
                    </div>
                </div>

                <!-- Strategy Performance -->
                <div class="card">
                    <h2 class="text-xl font-bold mb-4" id="strategy-title">
                        Strategy Performance (0)
                    </h2>
                    <div class="max-h-96 overflow-y-auto" id="strategy-list">
                        <p class="text-gray-400 text-center py-8">Loading...</p>
                    </div>
                </div>

                <!-- Learning Events -->
                <div class="card">
                    <h2 class="text-xl font-bold mb-4" id="learning-title">
                        Learning Events (0)
                    </h2>
                    <div class="max-h-96 overflow-y-auto" id="learning-list">
                        <p class="text-gray-400 text-center py-8">Loading...</p>
                    </div>
                </div>
            </div>

            <!-- Debug Info -->
            <div class="card">
                <h2 class="text-xl font-bold mb-4">Debug Information</h2>
                <pre id="debug-info" class="text-xs bg-gray-800 p-4 rounded overflow-auto max-h-64"></pre>
            </div>
        </div>
    </div>

    <script>
        // Enhanced WebSocket and API configuration
        const API_BASE = window.location.protocol === 'file:' ? 'http://localhost:8080' : '';
        const WS_URL = window.location.protocol === 'file:' ? 'ws://localhost:8080/ws' : `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws`;

        let debugLog = [];
        let websocket = null;
        let reconnectAttempts = 0;
        let maxReconnectAttempts = 5;
        let reconnectDelay = 1000;
        let isConnected = false;

        // Data pagination and caching
        let dataCache = new Map();
        let currentPage = { opportunities: 0, trades: 0, strategies: 0, learning: 0 };
        const pageSize = 20;

        // Performance metrics
        let performanceMetrics = {
            latency: 0,
            uptime: 0,
            cacheHitRatio: 0,
            errorRate: 0
        };

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.push(`[${timestamp}] ${message}`);
            document.getElementById('debug-info').textContent = debugLog.slice(-20).join('\n');
            console.log(message);
        }

        function updateStatus(status, message) {
            const statusEl = document.getElementById('status');
            statusEl.className = `px-4 py-2 rounded-lg mb-4 status-${status}`;
            statusEl.textContent = message;
        }

        // WebSocket connection management
        function initializeWebSocket() {
            try {
                websocket = new WebSocket(WS_URL);

                websocket.onopen = function(event) {
                    isConnected = true;
                    reconnectAttempts = 0;
                    log('WebSocket connected successfully');
                    updateStatus('success', '🔗 Real-time connection established');

                    // Subscribe to critical data updates
                    subscribeToUpdates();
                };

                websocket.onmessage = function(event) {
                    try {
                        const message = JSON.parse(event.data);
                        handleWebSocketMessage(message);
                    } catch (error) {
                        log(`Error parsing WebSocket message: ${error.message}`);
                    }
                };

                websocket.onclose = function(event) {
                    isConnected = false;
                    log(`WebSocket connection closed: ${event.code} - ${event.reason}`);
                    updateStatus('warning', '⚠️ Real-time connection lost, attempting to reconnect...');

                    // Attempt to reconnect
                    if (reconnectAttempts < maxReconnectAttempts) {
                        setTimeout(() => {
                            reconnectAttempts++;
                            log(`Reconnection attempt ${reconnectAttempts}/${maxReconnectAttempts}`);
                            initializeWebSocket();
                        }, reconnectDelay * Math.pow(2, reconnectAttempts));
                    } else {
                        updateStatus('error', '❌ Real-time connection failed, using polling mode');
                        startPollingMode();
                    }
                };

                websocket.onerror = function(error) {
                    log(`WebSocket error: ${error}`);
                };

            } catch (error) {
                log(`Failed to initialize WebSocket: ${error.message}`);
                startPollingMode();
            }
        }

        function subscribeToUpdates() {
            if (!websocket || websocket.readyState !== WebSocket.OPEN) return;

            const subscriptions = [
                { type: 'subscribe', data: { type: 'opportunity.updated' } },
                { type: 'subscribe', data: { type: 'trade.updated' } },
                { type: 'subscribe', data: { type: 'queue.updated' } },
                { type: 'subscribe', data: { type: 'system.health' } },
                { type: 'subscribe', data: { type: 'metrics.updated' } }
            ];

            subscriptions.forEach(sub => {
                websocket.send(JSON.stringify(sub));
            });

            log('Subscribed to real-time updates');
        }

        function handleWebSocketMessage(message) {
            const { type, data, timestamp } = message;

            switch (type) {
                case 'connection.established':
                    log(`Connected with client ID: ${data.clientId}`);
                    break;

                case 'opportunity.updated':
                    updateOpportunitiesFromWebSocket(data);
                    break;

                case 'trade.updated':
                    updateTradesFromWebSocket(data);
                    break;

                case 'queue.updated':
                    updateQueueFromWebSocket(data);
                    break;

                case 'system.health':
                    updateSystemHealthFromWebSocket(data);
                    break;

                case 'metrics.updated':
                    updateMetricsFromWebSocket(data);
                    break;

                default:
                    log(`Received unknown message type: ${type}`);
            }
        }

        function startPollingMode() {
            log('Starting polling mode for data updates');
            // Fallback to regular polling if WebSocket fails
            setInterval(loadData, 30000);
        }

        // WebSocket update handlers
        function updateOpportunitiesFromWebSocket(data) {
            if (Array.isArray(data)) {
                dataCache.set('opportunities', data);
                renderOpportunities(data);
                log(`Updated ${data.length} opportunities via WebSocket`);
            }
        }

        function updateTradesFromWebSocket(data) {
            if (Array.isArray(data)) {
                dataCache.set('trades', data);
                renderTrades(data);
                log(`Updated ${data.length} trades via WebSocket`);
            }
        }

        function updateQueueFromWebSocket(data) {
            if (data && data.status) {
                dataCache.set('queue', data);
                updateQueueDisplay(data);
                log('Updated queue status via WebSocket');
            }
        }

        function updateSystemHealthFromWebSocket(data) {
            if (data) {
                updateSystemHealthDisplay(data);
                log('Updated system health via WebSocket');
            }
        }

        function updateMetricsFromWebSocket(data) {
            if (data) {
                updatePerformanceMetrics(data);
                log('Updated performance metrics via WebSocket');
            }
        }

        // Progressive loading with pagination
        async function loadDataWithPagination(dataType, page = 0, limit = pageSize) {
            const cacheKey = `${dataType}_${page}_${limit}`;

            // Check cache first
            if (dataCache.has(cacheKey)) {
                const cachedData = dataCache.get(cacheKey);
                const cacheAge = Date.now() - cachedData.timestamp;

                // Use cached data if less than 5 minutes old for critical data
                if (cacheAge < (dataType.includes('opportunity') || dataType.includes('trade') ? 300000 : 60000)) {
                    return cachedData.data;
                }
            }

            try {
                const startTime = Date.now();
                const response = await fetch(`${API_BASE}/api/${dataType}?limit=${limit}&offset=${page * limit}`);
                const responseTime = Date.now() - startTime;

                if (!response.ok) throw new Error(`HTTP ${response.status}`);

                const result = await response.json();
                const data = result.success ? result.data : [];

                // Cache the result
                dataCache.set(cacheKey, {
                    data,
                    timestamp: Date.now(),
                    responseTime
                });

                // Update performance metrics
                performanceMetrics.latency = (performanceMetrics.latency + responseTime) / 2;

                return data;
            } catch (error) {
                log(`Error loading ${dataType}: ${error.message}`);
                performanceMetrics.errorRate++;
                return [];
            }
        }

        // Enhanced rendering functions with progressive loading
        function renderOpportunities(opportunities) {
            const container = document.getElementById('opportunities-list');
            document.getElementById('opportunities-title').textContent = `Live Opportunities (${opportunities.length})`;

            if (opportunities.length === 0) {
                container.innerHTML = '<p class="text-gray-400 text-center py-8">No opportunities detected</p>';
                return;
            }

            container.innerHTML = opportunities.map(opp => `
                <div class="p-3 bg-gray-600 hover:bg-gray-500 rounded-md transition-colors mb-2">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm font-medium text-blue-400">${opp.type}</span>
                        <span class="text-xs text-gray-400">${opp.network}</span>
                    </div>
                    <p class="text-sm text-gray-300">${opp.assets.join(' / ')}</p>
                    <p class="text-sm text-gray-400">${opp.exchanges.join(' & ')}</p>
                    <p class="text-lg font-bold text-green-400">$${(opp.potential_profit || opp.potentialProfit || 0).toFixed(2)}</p>
                </div>
            `).join('');
        }

        function renderTrades(trades) {
            const container = document.getElementById('trades-list');
            document.getElementById('trades-title').textContent = `Recent Trades (${trades.length})`;

            if (trades.length === 0) {
                container.innerHTML = '<p class="text-gray-400 text-center py-8">No trades executed</p>';
                return;
            }

            container.innerHTML = trades.map(trade => {
                const statusColor = trade.status === 'success' ? 'text-green-400' :
                                   trade.status === 'failed' ? 'text-red-400' : 'text-yellow-400';
                return `
                    <div class="p-3 bg-gray-600 hover:bg-gray-500 rounded-md transition-colors mb-2">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm font-medium text-purple-400">${trade.type}</span>
                            <span class="text-sm ${statusColor}">${trade.status.toUpperCase()}</span>
                        </div>
                        <p class="text-sm text-gray-300">${trade.assets.join(' / ')}</p>
                        <div class="flex justify-between">
                            <p class="text-lg font-bold text-green-400">+$${(trade.executed_profit || trade.executedProfit || 0).toFixed(2)}</p>
                            <p class="text-sm text-gray-400">Gas: $${(trade.gas_fees || trade.gasFees || 0).toFixed(2)}</p>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function renderStrategyPerformance(strategies) {
            const container = document.getElementById('strategy-list');
            document.getElementById('strategy-title').textContent = `Strategy Performance (${strategies.length})`;

            if (strategies.length === 0) {
                container.innerHTML = '<p class="text-gray-400 text-center py-8">No strategy data</p>';
                return;
            }

            container.innerHTML = strategies.map(strategy => `
                <div class="p-3 bg-gray-600 hover:bg-gray-500 rounded-md transition-colors mb-2">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm font-medium text-blue-400">${strategy.strategy_type}</span>
                        <span class="text-xs text-gray-400">${strategy.network}</span>
                    </div>
                    <div class="text-sm text-gray-300">Success: ${(strategy.success_rate || 0).toFixed(1)}%</div>
                    <div class="text-sm text-gray-300">Weight: ${(strategy.current_weight || 1.0).toFixed(2)}</div>
                    <div class="text-xs text-gray-400">Executions: ${strategy.total_executions || 0}</div>
                </div>
            `).join('');
        }

        function renderLearningEvents(events) {
            const container = document.getElementById('learning-list');
            document.getElementById('learning-title').textContent = `Learning Events (${events.length})`;

            if (events.length === 0) {
                container.innerHTML = '<p class="text-gray-400 text-center py-8">No learning events</p>';
                return;
            }

            container.innerHTML = events.map(event => {
                const eventColor = event.event_type === 'weight_update' ? 'text-green-400' :
                                 event.event_type === 'regime_change' ? 'text-purple-400' : 'text-blue-400';
                const timeAgo = getTimeAgo(new Date(event.created_at));

                return `
                    <div class="p-3 bg-gray-600 rounded-md mb-2">
                        <div class="flex justify-between items-center mb-1">
                            <span class="text-sm font-medium ${eventColor}">${event.event_type.replace('_', ' ')}</span>
                            <span class="text-xs text-gray-400">${timeAgo}</span>
                        </div>
                        <div class="text-sm text-gray-300">${event.strategy_type}</div>
                        <div class="text-xs text-gray-400">${event.reason}</div>
                        ${event.new_weight ? `<div class="text-xs text-green-400">Weight: ${event.new_weight.toFixed(3)}</div>` : ''}
                    </div>
                `;
            }).join('');
        }

        function getTimeAgo(date) {
            const now = new Date();
            const diffMs = now - date;
            const diffMins = Math.floor(diffMs / 60000);
            const diffHours = Math.floor(diffMins / 60);

            if (diffMins < 1) return 'just now';
            if (diffMins < 60) return `${diffMins}m ago`;
            if (diffHours < 24) return `${diffHours}h ago`;
            return `${Math.floor(diffHours / 24)}d ago`;
        }

        // Enhanced display functions
        function updateQueueDisplay(queueData) {
            // Add queue status display logic here
            log(`Queue status: ${queueData.length || 0} items`);
        }

        function updateSystemHealthDisplay(healthData) {
            // Update system health indicators
            if (healthData.databases) {
                Object.entries(healthData.databases).forEach(([db, health]) => {
                    log(`${db}: ${health.isHealthy ? 'healthy' : 'unhealthy'} (${health.latency}ms)`);
                });
            }
        }

        function updatePerformanceMetrics(metricsData) {
            if (metricsData.latency !== undefined) {
                performanceMetrics.latency = metricsData.latency;
            }
            if (metricsData.uptime !== undefined) {
                performanceMetrics.uptime = metricsData.uptime;
            }
            if (metricsData.cacheHitRatio !== undefined) {
                performanceMetrics.cacheHitRatio = metricsData.cacheHitRatio;
            }
        }

        async function loadData() {
            try {
                log('Loading data with enhanced features...');
                updateStatus('info', 'Loading backend data...');

                // Check backend health
                const healthResponse = await fetch(`${API_BASE}/health`);
                if (!healthResponse.ok) throw new Error('Backend not available');

                const health = await healthResponse.json();
                log(`Backend health: ${health.status}`);

                // Load data with progressive loading and caching
                const [opportunities, trades, strategies, learningEvents] = await Promise.all([
                    loadDataWithPagination('opportunities', currentPage.opportunities),
                    loadDataWithPagination('trades', currentPage.trades),
                    loadDataWithPagination('ml/strategy-performance', currentPage.strategies),
                    loadDataWithPagination('ml/learning-events', currentPage.learning)
                ]);

                // Load metrics and stats (less frequent updates)
                const [metricsRes, mlStatsRes] = await Promise.all([
                    fetch(`${API_BASE}/api/analytics/performance`),
                    fetch(`${API_BASE}/api/ml/learning-stats`)
                ]);

                const metricsData = await metricsRes.json();
                const mlStatsData = await mlStatsRes.json();

                const metrics = metricsData.success ? metricsData.data : null;
                const mlStats = mlStatsData.success ? mlStatsData.data : null;

                log(`Loaded: ${opportunities.length} opportunities, ${trades.length} trades, ${strategies.length} strategies, ${learningEvents.length} learning events`);

                // Update UI with enhanced rendering
                renderOpportunities(opportunities);
                renderTrades(trades);
                renderStrategyPerformance(strategies);
                renderLearningEvents(learningEvents);

                // Update metrics with performance data
                if (metrics) {
                    document.getElementById('total-profit').textContent = `$${metrics.netProfit.toFixed(2)}`;
                    document.getElementById('win-rate').textContent = `${metrics.winRate.toFixed(1)}%`;
                    document.getElementById('total-trades').textContent = metrics.totalTrades;
                    document.getElementById('daily-volume').textContent = `$${metrics.dailyVolume.toLocaleString()}`;
                }

                // Update ML metrics
                if (mlStats) {
                    document.getElementById('market-regime').textContent = mlStats.current_market_regime || 'Normal';
                    document.getElementById('active-strategies').textContent = mlStats.total_strategies || 0;
                    document.getElementById('learning-events').textContent = learningEvents.length;
                    document.getElementById('adaptation-rate').textContent = `${(mlStats.adaptation_rate || 0).toFixed(1)}/hr`;
                }

                // Update status with performance metrics
                const statusMessage = isConnected ?
                    `🔗 Real-time: ${opportunities.length} opportunities, ${trades.length} trades (${performanceMetrics.latency.toFixed(0)}ms avg)` :
                    `📊 Polling: ${opportunities.length} opportunities, ${trades.length} trades`;

                updateStatus('success', statusMessage);
                log('Enhanced data loading completed successfully');

            } catch (err) {
                log(`Error loading data: ${err.message}`);
                updateStatus('error', `Error: ${err.message}`);
                performanceMetrics.errorRate++;
            }
        }

        // Initialize enhanced dashboard
        log('Initializing Enhanced MEV Arbitrage Bot Dashboard...');

        // Initialize WebSocket connection for real-time updates
        initializeWebSocket();

        // Load initial data
        loadData();

        // Set up different refresh intervals based on data criticality
        // Critical data (opportunities, trades, queue) - every 5 seconds via WebSocket
        // Performance metrics - every 30 seconds
        setInterval(async () => {
            if (!isConnected) {
                // Only poll if WebSocket is not connected
                await loadData();
            }
        }, 30000);

        // Historical analytics - every 60 seconds
        setInterval(async () => {
            try {
                const metricsRes = await fetch(`${API_BASE}/api/analytics/performance`);
                const metricsData = await metricsRes.json();
                if (metricsData.success && metricsData.data) {
                    const metrics = metricsData.data;
                    document.getElementById('total-profit').textContent = `$${metrics.netProfit.toFixed(2)}`;
                    document.getElementById('win-rate').textContent = `${metrics.winRate.toFixed(1)}%`;
                    document.getElementById('total-trades').textContent = metrics.totalTrades;
                    document.getElementById('daily-volume').textContent = `$${metrics.dailyVolume.toLocaleString()}`;
                }
            } catch (error) {
                log(`Error updating analytics: ${error.message}`);
            }
        }, 60000);

        // Performance monitoring display
        setInterval(() => {
            if (performanceMetrics.latency > 0) {
                log(`Performance: ${performanceMetrics.latency.toFixed(0)}ms avg latency, ${performanceMetrics.cacheHitRatio.toFixed(1)}% cache hit ratio`);
            }
        }, 120000); // Every 2 minutes
    </script>
</body>
</html>
