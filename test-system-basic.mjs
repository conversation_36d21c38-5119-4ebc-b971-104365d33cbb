#!/usr/bin/env node

/**
 * Basic System Integration Test
 * 
 * This script performs a simple validation of the system components
 * without complex dependencies to verify basic functionality.
 */

import { promises as fs } from 'fs';
import path from 'path';

console.log('🚀 Starting Basic System Integration Test...');

async function testBasicSystemIntegration() {
  const results = [];
  
  try {
    // Test 1: Check if core service files exist
    console.log('\n📋 Test 1: Checking core service files...');
    
    const coreServices = [
      'backend/services/ServiceIntegrator.ts',
      'backend/services/ProfitValidationService.ts',
      'backend/services/EnhancedTokenMonitoringService.ts',
      'backend/services/ProfitPrioritizedExecutionQueue.ts',
      'backend/services/FlashLoanService.ts',
      'backend/services/MEVProtectionService.ts',
      'backend/services/PreExecutionValidationService.ts'
    ];
    
    let missingFiles = 0;
    for (const service of coreServices) {
      try {
        await fs.access(service);
        console.log(`   ✅ ${service}`);
      } catch (error) {
        console.log(`   ❌ ${service} - MISSING`);
        missingFiles++;
      }
    }
    
    if (missingFiles === 0) {
      results.push({ test: 'Core Service Files', status: 'PASS', message: 'All core service files present' });
    } else {
      results.push({ test: 'Core Service Files', status: 'FAIL', message: `${missingFiles} files missing` });
    }
    
    // Test 2: Check configuration file
    console.log('\n📋 Test 2: Checking configuration...');
    
    try {
      const configContent = await fs.readFile('backend/config/index.ts', 'utf8');
      
      const requiredConfigs = [
        'ENABLE_PROFIT_VALIDATION',
        'ENABLE_ENHANCED_TOKEN_MONITORING',
        'ENABLE_FLASH_LOANS',
        'PROFIT_VALIDATION_THRESHOLD',
        'MAX_SERVICE_LATENCY'
      ];
      
      let missingConfigs = 0;
      for (const config of requiredConfigs) {
        if (configContent.includes(config)) {
          console.log(`   ✅ ${config}`);
        } else {
          console.log(`   ❌ ${config} - MISSING`);
          missingConfigs++;
        }
      }
      
      if (missingConfigs === 0) {
        results.push({ test: 'Configuration', status: 'PASS', message: 'All required configurations present' });
      } else {
        results.push({ test: 'Configuration', status: 'FAIL', message: `${missingConfigs} configurations missing` });
      }
      
    } catch (error) {
      results.push({ test: 'Configuration', status: 'FAIL', message: 'Configuration file not accessible' });
    }
    
    // Test 3: Check test files
    console.log('\n📋 Test 3: Checking integration test files...');
    
    const testFiles = [
      'tests/integration/enhanced-system-integration.test.ts',
      'tests/e2e/complete-arbitrage-workflow.test.ts',
      'tests/performance/enhanced-benchmark.test.ts'
    ];
    
    let missingTests = 0;
    for (const testFile of testFiles) {
      try {
        await fs.access(testFile);
        console.log(`   ✅ ${testFile}`);
      } catch (error) {
        console.log(`   ❌ ${testFile} - MISSING`);
        missingTests++;
      }
    }
    
    if (missingTests === 0) {
      results.push({ test: 'Test Files', status: 'PASS', message: 'All test files present' });
    } else {
      results.push({ test: 'Test Files', status: 'FAIL', message: `${missingTests} test files missing` });
    }
    
    // Test 4: Check package.json scripts
    console.log('\n📋 Test 4: Checking package.json scripts...');
    
    try {
      const packageContent = await fs.readFile('package.json', 'utf8');
      const packageJson = JSON.parse(packageContent);
      
      const requiredScripts = [
        'test:enhanced',
        'test:workflow',
        'test:benchmark',
        'test:comprehensive',
        'validate:system',
        'demo:integration'
      ];
      
      let missingScripts = 0;
      for (const script of requiredScripts) {
        if (packageJson.scripts && packageJson.scripts[script]) {
          console.log(`   ✅ ${script}`);
        } else {
          console.log(`   ❌ ${script} - MISSING`);
          missingScripts++;
        }
      }
      
      if (missingScripts === 0) {
        results.push({ test: 'Package Scripts', status: 'PASS', message: 'All required scripts present' });
      } else {
        results.push({ test: 'Package Scripts', status: 'FAIL', message: `${missingScripts} scripts missing` });
      }
      
    } catch (error) {
      results.push({ test: 'Package Scripts', status: 'FAIL', message: 'package.json not accessible' });
    }
    
    // Test 5: Check documentation
    console.log('\n📋 Test 5: Checking documentation...');
    
    const docFiles = [
      'SYSTEM_INTEGRATION_TESTING.md',
      'COMPREHENSIVE_INTEGRATION_TESTING_COMPLETE.md'
    ];
    
    let missingDocs = 0;
    for (const docFile of docFiles) {
      try {
        await fs.access(docFile);
        console.log(`   ✅ ${docFile}`);
      } catch (error) {
        console.log(`   ❌ ${docFile} - MISSING`);
        missingDocs++;
      }
    }
    
    if (missingDocs === 0) {
      results.push({ test: 'Documentation', status: 'PASS', message: 'All documentation files present' });
    } else {
      results.push({ test: 'Documentation', status: 'FAIL', message: `${missingDocs} documentation files missing` });
    }
    
    // Test 6: Basic TypeScript compilation check
    console.log('\n📋 Test 6: Checking TypeScript compilation...');
    
    try {
      // Check if we can at least parse the main service integrator
      const serviceIntegratorContent = await fs.readFile('backend/services/ServiceIntegrator.ts', 'utf8');
      
      // Basic syntax checks
      const hasClassDeclaration = serviceIntegratorContent.includes('export class ServiceIntegrator');
      const hasInitializeMethod = serviceIntegratorContent.includes('async initialize()');
      const hasHealthCheckMethod = serviceIntegratorContent.includes('healthCheck()');
      
      if (hasClassDeclaration && hasInitializeMethod && hasHealthCheckMethod) {
        console.log('   ✅ ServiceIntegrator class structure valid');
        results.push({ test: 'TypeScript Structure', status: 'PASS', message: 'Core service structure valid' });
      } else {
        console.log('   ❌ ServiceIntegrator class structure invalid');
        results.push({ test: 'TypeScript Structure', status: 'FAIL', message: 'Core service structure invalid' });
      }
      
    } catch (error) {
      results.push({ test: 'TypeScript Structure', status: 'FAIL', message: 'Cannot access TypeScript files' });
    }
    
    // Display Results
    console.log('\n' + '='.repeat(80));
    console.log('🏁 BASIC SYSTEM INTEGRATION TEST RESULTS');
    console.log('='.repeat(80));
    
    const passedTests = results.filter(r => r.status === 'PASS').length;
    const failedTests = results.filter(r => r.status === 'FAIL').length;
    const totalTests = results.length;
    
    console.log(`📊 Total Tests: ${totalTests}`);
    console.log(`✅ Passed: ${passedTests}`);
    console.log(`❌ Failed: ${failedTests}`);
    console.log(`📈 Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    console.log('\n📋 Detailed Results:');
    results.forEach((result, index) => {
      const statusIcon = result.status === 'PASS' ? '✅' : '❌';
      console.log(`${index + 1}. ${statusIcon} ${result.test}: ${result.message}`);
    });
    
    console.log('\n' + '='.repeat(80));
    
    if (failedTests === 0) {
      console.log('🎉 ALL BASIC INTEGRATION TESTS PASSED!');
      console.log('✅ System structure is complete and ready for advanced testing');
      console.log('\n🚀 Next Steps:');
      console.log('   1. Run: npm run validate:system');
      console.log('   2. Run: npm run test:comprehensive');
      console.log('   3. Run: npm run demo:integration');
    } else {
      console.log(`⚠️  ${failedTests} test(s) failed. System structure needs attention.`);
      console.log('\n🔧 Recommended Actions:');
      console.log('   1. Review failed tests above');
      console.log('   2. Ensure all files are properly created');
      console.log('   3. Check TypeScript compilation');
    }
    
    return failedTests === 0;
    
  } catch (error) {
    console.error('❌ Basic system integration test failed:', error);
    return false;
  }
}

// Run the test
testBasicSystemIntegration()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('Test runner failed:', error);
    process.exit(1);
  });
