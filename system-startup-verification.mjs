#!/usr/bin/env node

// Comprehensive system startup verification for MEV Arbitrage Bot
import fetch from 'node-fetch';
import { existsSync } from 'fs';
import { join } from 'path';

const API_BASE = 'http://localhost:8080';

console.log('🚀 MEV Arbitrage Bot - System Startup Verification');
console.log('==================================================\n');

async function verifySystem() {
    let allChecks = [];
    
    // 1. File System Checks
    console.log('📁 File System Checks:');
    const files = [
        'working-backend.mjs',
        'index.html',
        'package.json',
        'docker-compose.yml',
        'contracts/ArbitrageExecutor.sol',
        'backend/server.ts'
    ];
    
    for (const file of files) {
        const exists = existsSync(file);
        console.log(`   ${exists ? '✅' : '❌'} ${file}`);
        allChecks.push({ check: `File: ${file}`, status: exists });
    }
    
    // 2. Backend API Checks
    console.log('\n📡 Backend API Checks:');
    try {
        const healthRes = await fetch(`${API_BASE}/health`);
        const health = await healthRes.json();
        console.log(`   ✅ Health endpoint: ${health.status}`);
        allChecks.push({ check: 'Backend Health', status: true });
        
        const endpoints = [
            { path: '/api/opportunities', name: 'Opportunities API' },
            { path: '/api/trades', name: 'Trades API' },
            { path: '/api/tokens', name: 'Tokens API' },
            { path: '/api/analytics/performance', name: 'Analytics API' },
            { path: '/api/system/health', name: 'System Health API' }
        ];
        
        for (const endpoint of endpoints) {
            try {
                const res = await fetch(`${API_BASE}${endpoint.path}`);
                const data = await res.json();
                const success = data.success;
                console.log(`   ${success ? '✅' : '❌'} ${endpoint.name}`);
                allChecks.push({ check: endpoint.name, status: success });
            } catch (err) {
                console.log(`   ❌ ${endpoint.name}: ${err.message}`);
                allChecks.push({ check: endpoint.name, status: false });
            }
        }
    } catch (err) {
        console.log(`   ❌ Backend not accessible: ${err.message}`);
        allChecks.push({ check: 'Backend Accessibility', status: false });
    }
    
    // 3. Data Validation
    console.log('\n📊 Data Validation:');
    try {
        const [oppRes, tradesRes, tokensRes, metricsRes] = await Promise.all([
            fetch(`${API_BASE}/api/opportunities`),
            fetch(`${API_BASE}/api/trades`),
            fetch(`${API_BASE}/api/tokens`),
            fetch(`${API_BASE}/api/analytics/performance`)
        ]);
        
        const [oppData, tradesData, tokensData, metricsData] = await Promise.all([
            oppRes.json(),
            tradesRes.json(),
            tokensRes.json(),
            metricsRes.json()
        ]);
        
        console.log(`   ✅ Opportunities: ${oppData.data?.length || 0} items`);
        console.log(`   ✅ Trades: ${tradesData.data?.length || 0} items`);
        console.log(`   ✅ Tokens: ${tokensData.data?.length || 0} items`);
        console.log(`   ✅ Metrics: ${metricsData.data ? 'Available' : 'Not Available'}`);
        
        allChecks.push({ check: 'Data Availability', status: true });
        
    } catch (err) {
        console.log(`   ❌ Data validation failed: ${err.message}`);
        allChecks.push({ check: 'Data Availability', status: false });
    }
    
    // 4. System Summary
    console.log('\n📈 System Summary:');
    const passedChecks = allChecks.filter(c => c.status).length;
    const totalChecks = allChecks.length;
    const successRate = ((passedChecks / totalChecks) * 100).toFixed(1);
    
    console.log(`   ✅ Passed: ${passedChecks}/${totalChecks} checks (${successRate}%)`);
    
    if (passedChecks === totalChecks) {
        console.log('\n🎉 SYSTEM FULLY OPERATIONAL!');
        console.log('🌐 Frontend Dashboard: Open index.html in your browser');
        console.log('📡 Backend API: http://localhost:8080');
        console.log('❤️  Health Check: http://localhost:8080/health');
        console.log('\n🎯 Next Steps:');
        console.log('1. ✅ Backend is running');
        console.log('2. ✅ Frontend is accessible');
        console.log('3. 🔄 Monitor the dashboard for live data');
        console.log('4. 📊 Check analytics and performance metrics');
    } else {
        console.log('\n⚠️  SYSTEM PARTIALLY OPERATIONAL');
        console.log('❌ Failed checks:');
        allChecks.filter(c => !c.status).forEach(c => {
            console.log(`   - ${c.check}`);
        });
    }
    
    console.log('\n🔧 System Status: READY FOR OPERATION');
}

verifySystem().catch(console.error);
