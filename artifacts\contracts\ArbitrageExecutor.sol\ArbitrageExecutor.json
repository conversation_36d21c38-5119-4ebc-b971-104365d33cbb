{"_format": "hh-sol-artifact-1", "contractName": "ArbitrageExecutor", "sourceName": "contracts/ArbitrageExecutor.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_tokenDiscovery", "type": "address"}, {"internalType": "address", "name": "_liquidityChecker", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "tradeId", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "executor", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "profit", "type": "uint256"}, {"indexed": false, "internalType": "uint8", "name": "arbitrageType", "type": "uint8"}], "name": "ArbitrageExecuted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "asset", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "premium", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "success", "type": "bool"}], "name": "FlashLoanExecuted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "ProfitWithdrawn", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [{"internalType": "address", "name": "executor", "type": "address"}], "name": "addAuthorizedExecutor", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "authorizedExecutors", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "address[]", "name": "tokens", "type": "address[]"}, {"internalType": "address[]", "name": "exchanges", "type": "address[]"}, {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}, {"internalType": "uint256", "name": "minProfit", "type": "uint256"}, {"internalType": "uint8", "name": "arbitrageType", "type": "uint8"}, {"internalType": "bytes", "name": "routeData", "type": "bytes"}], "internalType": "struct ArbitrageExecutor.ArbitrageParams", "name": "params", "type": "tuple"}, {"internalType": "address", "name": "flashLoanProvider", "type": "address"}, {"internalType": "uint256", "name": "flashLoanAmount", "type": "uint256"}], "name": "executeArbitrage", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "assets", "type": "address[]"}, {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "premiums", "type": "uint256[]"}, {"internalType": "address", "name": "initiator", "type": "address"}, {"internalType": "bytes", "name": "params", "type": "bytes"}], "name": "executeOperation", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "executorNonces", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getStats", "outputs": [{"internalType": "uint256", "name": "totalProfit", "type": "uint256"}, {"internalType": "uint256", "name": "totalTrades", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "liquidityChecker", "outputs": [{"internalType": "contract LiquidityChecker", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "maxSlippage", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "minProfitThreshold", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "executor", "type": "address"}], "name": "removeAuthorizedExecutor", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "tokenDiscovery", "outputs": [{"internalType": "contract TokenDiscovery", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalProfitGenerated", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalTradesExecuted", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newThreshold", "type": "uint256"}], "name": "updateMinProfitThreshold", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "withdrawProfits", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x60c03461011857601f61187038819003918201601f19168301916001600160401b0383118484101761011d57808492604094855283398101031261011857610052602061004b83610133565b9201610133565b9033156100ff5760008054336001600160a01b0319821681178355604080519590946001600160a01b03939192849283167f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e08780a3600180556802b5e3af16b18800006007556101f4600855166080521660a052338152600360205220600160ff19825416179055611728908161014882396080518181816103db01526113e6015260a051816101f20152f35b604051631e4fbdf760e01b815260006004820152602490fd5b600080fd5b634e487b7160e01b600052604160045260246000fd5b51906001600160a01b03821682036101185756fe608080604052600436101561001357600080fd5b60003560e01c9081631360003f146114155750806329fb2c3a146113d05780633f4ba83a1461136657806358d0ee58146113455780635c975abb1461132257806360c7bae2146112e0578063679d86a7146112a1578063715018a6146112485780637341ab8c1461122a5780638456cb59146111d05780638c04166f146111b25780638da5cb5b14611189578063920f5c84146109da578063a5a932b4146109a0578063a9abd6331461033b578063aafc965114610266578063b2a7f49414610221578063bc2874a8146101dc578063c59d4847146101b7578063d2a037ab146101995763f2fde38b1461010657600080fd5b346101945760203660031901126101945761011f611430565b61012761148a565b6001600160a01b0390811690811561017b57600054826bffffffffffffffffffffffff60a01b821617600055167f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0600080a3005b604051631e4fbdf760e01b815260006004820152602490fd5b600080fd5b34610194576000366003190112610194576020600654604051908152f35b3461019457600036600319011261019457604060055460065482519182526020820152f35b34610194576000366003190112610194576040517f00000000000000000000000000000000000000000000000000000000000000006001600160a01b03168152602090f35b346101945760203660031901126101945761023a611430565b61024261148a565b6001600160a01b03166000908152600360205260409020805460ff19166001179055005b346101945760403660031901126101945761027f611430565b6024359061028b61148a565b6001600160a01b03169081156103045780156102ce5760207f016e128b6bdadd9e9068abd0b18db2fc8b27ed3dbced50e4aa6cc0a6934251ab91604051908152a2005b60405162461bcd60e51b815260206004820152600e60248201526d125b9d985b1a5908185b5bdd5b9d60921b6044820152606490fd5b60405162461bcd60e51b815260206004820152600f60248201526e496e76616c6964206164647265737360881b6044820152606490fd5b34610194576060366003190112610194576001600160401b03600435116101945760c060043536036003190112610194576024356001600160a01b038116036101945733600052600360205260ff60406000205416801561098c575b15610947576103a46115e8565b60026001541461093557600260018190556103c36004803501806114b6565b9050106108fa5760075460646004350135106108bc577f00000000000000000000000000000000000000000000000000000000000000006001600160a01b031660005b6104146004803501806114b6565b905081101561050f5761042b6004803501806114b6565b8210156104f957610440908260051b016114fa565b6040516360af135d60e11b81526001600160a01b039091166004820152602081602481865afa9081156104ed576000916104be575b501561048957610484906114eb565b610406565b60405162461bcd60e51b815260206004820152600d60248201526c24b73b30b634b2103a37b5b2b760991b6044820152606490fd5b6104e0915060203d6020116104e6575b6104d88183611529565b81019061154a565b83610475565b503d6104ce565b6040513d6000823e3d90fd5b634e487b7160e01b600052603260045260246000fd5b3360005260046020526040600020805490610529826114eb565b90556105396004803501806114b6565b6040513360601b602082015260348101939093524260548401528291607483019160005b81811061088e57505050039061057b601f1992838101835282611529565b602081519101206040519060208201526040808201526105ba6105a8600435600401600435600401611562565b60c06060850152610120840191611596565b6105e56105d1602460043501600435600401611562565b605f19858503810160808701529391611596565b6105f9604460043501600435600401611562565b84830390930160a0850152828252916001600160fb1b0381116101945760051b80926020830137016064600435013560c083015260ff61063d6084600435016115da565b1660e083015260043560a48101359036036022190181121561019457600435016024600482013591016001600160401b038211610194578136038113610194576106ba9285601f846040958895603f1987870301610100880152816020870152878601376000868286010152011601036020810184520182611529565b6106c86004803501806114b6565b156104f9576106d6906114fa565b91604051926106e48461150e565b60018452602084019060203683376106fb8561169d565b6001600160a01b039091169052604051906107158261150e565b6001825260203681840137604435918261072e8261169d565b526040519261073c8461150e565b600184526020368186013760098102908082046009149015171561087857868585856127108a950461076d8361169d565b52604051632483d72160e21b815260a06004820152935160a485018190529395869460c48601929060005b818110610856575050508482036003190160248601526107ce92916107bc916116be565b848103600319016044860152906116be565b3060648401528281036003190160848401528351808252909360005b82811061083f578460208181818a89601f8a60008682860101520116010301816000305af180156104ed57610820575b60018055005b6108389060203d6020116104e6576104d88183611529565b508061081a565b6020828201810151878301820152879550016107ea565b82516001600160a01b0316855289975060209485019490920191600101610798565b634e487b7160e01b600052601160045260246000fd5b9193509160019060209081906001600160a01b036108ab88611446565b16815201940191019184939261055d565b60405162461bcd60e51b8152602060048201526016602482015275141c9bd99a5d0818995b1bddc81d1a1c995cda1bdb1960521b6044820152606490fd5b60405162461bcd60e51b8152602060048201526013602482015272125b9d985b1a59081d1bdad95b8818dbdd5b9d606a1b6044820152606490fd5b604051633ee5aeb560e01b8152600490fd5b60405162461bcd60e51b815260206004820152601760248201527f4e6f7420617574686f72697a6564206578656375746f720000000000000000006044820152606490fd5b506000546001600160a01b03163314610397565b34610194576020366003190112610194576001600160a01b036109c1611430565b1660005260046020526020604060002054604051908152f35b346101945760a0366003190112610194576004356001600160401b03811161019457610a0a90369060040161145a565b906024356001600160401b03811161019457610a2a90369060040161145a565b916044356001600160401b03811161019457610a4a90369060040161145a565b606435926001600160a01b0384168403610194576001600160401b03608435116101945736602360843501121561019457608435600401356001600160401b03811161019457608435019636602489011161019457306001600160a01b038616036111505760406084358903126101945760446084350135976001600160401b0389116101945760c0896084350182031261019457604051988960c08101106001600160401b0360c08c01111761112a5760c08a016040526001600160401b036024826084350101351161019457610b31602483810190608435840180820135010161161d565b8a526001600160401b036044826084350101351161019457610b6360248381019060843584016044810135010161161d565b60208b01526064816084350101356001600160401b038111610194576024830160438284608435010101121561019457610ba66024828460843501010135611606565b90610bb46040519283611529565b6024608435840182018181013580855260208501928701604460059290921b90920101116101945760448285608435010101905b60843585018301602481013560051b0160440182106111405750505060408b01526084803582019081013560608c0152610c249060a4016115da565b60808b015260c4816084350101356001600160401b0381116101945760248301604382846084350101011215610194576001600160401b0360248284608435010101351161112a57602460405193610c8f6020601f19601f8587896084350101013501160186611529565b6084358401830180830135865291016024820135909101604401116101945760009160209160249160843581018201808401359060440187860137608435010101358301015260a0890152156104f95760249560206001600160a01b03610cf5846114fa565b16604051988980926370a0823160e01b82523060048301525afa9687156104ed576000976110f6575b501591826104f9571590816104f957610d3984358735611683565b600060ff60808b015116801560001461106357505060005b60208a015151806000198101116108785760001901811015610db157610d78818b516116aa565b5089519060018101811161087857610d97610dac9260018301906116aa565b50610da68160408d01516116aa565b506114eb565b610d51565b509092949697919395976001975b602460206001600160a01b03610dd4886114fa565b16604051928380926370a0823160e01b82523060048301525afa9283156104ed57849160009461102c575b508084111561102357610e129084611690565b925b10610fdf5760608401518210610f9a5760009260209060446001600160a01b03610e3d896114fa565b1691604051968793849263095ea7b360e01b845233600485015260248401525af19283156104ed578a93610f7b575b508880610f72575b610ee0575b50505050610e86906114fa565b916104f9576104f957604080519435855291356020850152911515908301526001600160a01b0316907fe7055bb7f757aae8cfbf6ea2247e26371914425f5500c1d720f0746ae1ff880390606090a2602060405160018152f35b90919450610e869392955060ff6080600097610f15610f0d610f0760009a8d359035611683565b87611690565b600554611683565b600555610f236006546114eb565b600655015116604051928352602083015260018060a01b0316907f02c11b9decb09cec5212b5b3f9c5241911f597637a4586b573e7c3afa8f6c84760406024608435013592a390868880610e79565b50811515610e74565b610f939060203d6020116104e6576104d88183611529565b508a610e6c565b60405162461bcd60e51b815260206004820152601e60248201527f50726f6669742062656c6f77206d696e696d756d207468726573686f6c6400006044820152606490fd5b606460405162461bcd60e51b815260206004820152602060248201527f496e73756666696369656e742066756e647320746f207265706179206c6f616e6044820152fd5b50600092610e14565b915092506020813d60201161105b575b8161104960209383611529565b8101031261019457839051928c610dff565b3d915061103c565b6001819b9597999b9a939496989a14600014611083575050600197610dbf565b90989060011901610dbf5797506003835151036110a257600197610dbf565b60405162461bcd60e51b815260206004820152602660248201527f547269616e67756c617220617262697472616765207265717569726573203320604482015265746f6b656e7360d01b6064820152608490fd5b9096506020813d602011611122575b8161111260209383611529565b8101031261019457519588610d1e565b3d9150611105565b634e487b7160e01b600052604160045260246000fd5b8135815260209182019101610be8565b60405162461bcd60e51b815260206004820152601160248201527024b73b30b634b21034b734ba34b0ba37b960791b6044820152606490fd5b34610194576000366003190112610194576000546040516001600160a01b039091168152602090f35b34610194576000366003190112610194576020600854604051908152f35b34610194576000366003190112610194576111e961148a565b6111f16115e8565b600160ff1960025416176002557f62e78cea01bee320cd4e420270b5ea74000d11b0c9f74754ebdbfc544b05a2586020604051338152a1005b34610194576000366003190112610194576020600754604051908152f35b346101945760003660031901126101945761126161148a565b600080546001600160a01b0319811682556001600160a01b03167f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e08280a3005b34610194576020366003190112610194576001600160a01b036112c2611430565b166000526003602052602060ff604060002054166040519015158152f35b34610194576020366003190112610194576112f9611430565b61130161148a565b6001600160a01b03166000908152600360205260409020805460ff19169055005b3461019457600036600319011261019457602060ff600254166040519015158152f35b346101945760203660031901126101945761135e61148a565b600435600755005b346101945760003660031901126101945761137f61148a565b60025460ff8116156113be5760ff19166002557f5db9ee0a495bf2e6ff9c91a7834c1ba4fdd244a5e8aa4e537bd38aeae4b073aa6020604051338152a1005b604051638dfc202b60e01b8152600490fd5b34610194576000366003190112610194576040517f00000000000000000000000000000000000000000000000000000000000000006001600160a01b03168152602090f35b34610194576000366003190112610194576020906005548152f35b600435906001600160a01b038216820361019457565b35906001600160a01b038216820361019457565b9181601f84011215610194578235916001600160401b038311610194576020808501948460051b01011161019457565b6000546001600160a01b0316330361149e57565b60405163118cdaa760e01b8152336004820152602490fd5b903590601e198136030182121561019457018035906001600160401b03821161019457602001918160051b3603831361019457565b60001981146108785760010190565b356001600160a01b03811681036101945790565b604081019081106001600160401b0382111761112a57604052565b90601f801991011681019081106001600160401b0382111761112a57604052565b90816020910312610194575180151581036101945790565b9035601e19823603018112156101945701602081359101916001600160401b038211610194578160051b3603831361019457565b91908082526020809201929160005b8281106115b3575050505090565b909192938280600192838060a01b036115cb89611446565b168152019501939291016115a5565b359060ff8216820361019457565b60ff600254166115f457565b60405163d93c066560e01b8152600490fd5b6001600160401b03811161112a5760051b60200190565b81601f820112156101945780359161163483611606565b926116426040519485611529565b808452602092838086019260051b820101928311610194578301905b82821061166c575050505090565b83809161167884611446565b81520191019061165e565b9190820180921161087857565b9190820391821161087857565b8051156104f95760200190565b80518210156104f95760209160051b010190565b90815180825260208080930193019160005b8281106116de575050505090565b8351855293810193928101926001016116d056fea2646970667358221220dec2fd6f0adf340e71250b18e223868485fe1f117aa03e9dbe49a1442865138d64736f6c63430008140033", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}