#!/usr/bin/env node

/**
 * MEV Arbitrage Bot - Production Startup Script
 * 
 * This script provides a comprehensive startup sequence for the MEV arbitrage bot,
 * ensuring all components are properly initialized and tested before going live.
 */

// Load environment variables FIRST before any other imports
import { config } from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables before importing any modules that might use them
config({ path: join(__dirname, '.env') });

// Now import other modules after environment is loaded
import logger from './backend/utils/logger.js';
import { SystemInitializer } from './backend/startup/SystemInitializer.js';
import { Server } from './backend/startup/Server.js';

class MEVArbitrageBotStartup {
  private systemInitializer: SystemInitializer;
  private server: Server | null = null;
  private isShuttingDown = false;

  constructor() {
    this.systemInitializer = new SystemInitializer();
    this.setupEventHandlers();
  }

  public async start(): Promise<void> {
    try {
      this.printStartupBanner();
      
      // Setup graceful shutdown handlers
      this.systemInitializer.setupGracefulShutdown();
      this.setupCustomShutdownHandlers();

      logger.info('🚀 Starting MEV Arbitrage Bot...');
      logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
      logger.info(`Node.js Version: ${process.version}`);
      logger.info(`Platform: ${process.platform} ${process.arch}`);

      // Initialize system
      const initResult = await this.systemInitializer.initialize();

      if (!initResult.success) {
        logger.error('❌ System initialization failed');
        this.logInitializationErrors(initResult);
        process.exit(1);
      }

      logger.info('✅ System initialization completed successfully');
      this.logInitializationSuccess(initResult);

      // Start HTTP server
      await this.startServer();

      // System is now ready
      this.logSystemReady();
      
      // Start monitoring and reporting
      this.startPeriodicReporting();

    } catch (error) {
      logger.error('❌ Fatal error during startup:', error);
      await this.shutdown();
      process.exit(1);
    }
  }

  private async startServer(): Promise<void> {
    try {
      logger.info('🌐 Starting HTTP server...');
      
      this.server = new Server();
      
      // Pass initialized services to server
      const serviceManager = this.systemInitializer.getServiceManager();
      const healthMonitor = this.systemInitializer.getHealthMonitor();

      this.server.setServiceManager(serviceManager);
      this.server.setHealthMonitor(healthMonitor);
      
      await this.server.start();
      
      logger.info('✅ HTTP server started successfully');
      
    } catch (error) {
      logger.error('❌ Failed to start HTTP server:', error);
      throw error;
    }
  }

  private setupEventHandlers(): void {
    // System initialization events
    this.systemInitializer.on('initializationStarted', () => {
      logger.info('📋 System initialization started...');
    });

    this.systemInitializer.on('initializationCompleted', (result) => {
      logger.info(`🎉 System initialization completed in ${result.startupTime}ms`);
    });

    this.systemInitializer.on('initializationFailed', (result) => {
      logger.error('❌ System initialization failed');
      logger.error(`Errors: ${result.errors.join(', ')}`);
    });

    // Service events
    this.systemInitializer.on('serviceStarted', (serviceName) => {
      logger.info(`✅ Service started: ${serviceName}`);
    });

    this.systemInitializer.on('serviceFailed', (serviceName, error) => {
      logger.error(`❌ Service failed: ${serviceName}`, error);
    });

    this.systemInitializer.on('serviceUnhealthy', (serviceName) => {
      logger.warn(`⚠️ Service unhealthy: ${serviceName}`);
    });

    // Health monitoring events
    this.systemInitializer.on('healthCheckCompleted', (status) => {
      const healthyServices = Object.values(status.services).filter(Boolean).length;
      const totalServices = Object.keys(status.services).length;
      
      if (healthyServices < totalServices) {
        logger.warn(`Health check: ${healthyServices}/${totalServices} services healthy`);
      }
    });

    // ML learning events
    this.systemInitializer.on('mlLearningUpdate', (update) => {
      logger.debug(`ML learning update: ${update.totalStrategies} strategies, regime: ${update.marketRegime}`);
    });

    this.systemInitializer.on('strategyUpdate', (update) => {
      logger.debug(`Strategy update: ${update.availableStrategies} strategies available`);
    });
  }

  private setupCustomShutdownHandlers(): void {
    // Custom shutdown handling for this startup script
    const gracefulShutdown = async (signal: string) => {
      if (this.isShuttingDown) return;
      
      this.isShuttingDown = true;
      logger.info(`Received ${signal}. Starting graceful shutdown...`);
      
      await this.shutdown();
      process.exit(0);
    };

    // Override default handlers to include server shutdown
    process.removeAllListeners('SIGTERM');
    process.removeAllListeners('SIGINT');
    process.removeAllListeners('SIGUSR2');

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    process.on('SIGUSR2', () => gracefulShutdown('SIGUSR2'));
  }

  private async shutdown(): Promise<void> {
    if (this.isShuttingDown) return;
    this.isShuttingDown = true;

    logger.info('🛑 Starting system shutdown...');

    try {
      // Stop HTTP server first
      if (this.server) {
        logger.info('Stopping HTTP server...');
        await this.server.stop();
        logger.info('✅ HTTP server stopped');
      }

      // Shutdown system components
      await this.systemInitializer.shutdown();
      
      logger.info('✅ System shutdown completed');
      
    } catch (error) {
      logger.error('❌ Error during shutdown:', error);
    }
  }

  private printStartupBanner(): void {
    const banner = `
╔══════════════════════════════════════════════════════════════╗
║                    MEV ARBITRAGE BOT                         ║
║                                                              ║
║  🤖 Intelligent Multi-Chain Arbitrage Trading System        ║
║  🧠 Adaptive Machine Learning Strategy Selection            ║
║  🔗 Cross-Chain Opportunity Detection                       ║
║  ⚡ Real-Time Execution & Risk Management                   ║
║                                                              ║
║  Version: 1.0.0                                             ║
║  Environment: ${(process.env.NODE_ENV || 'development').padEnd(11)}                                    ║
╚══════════════════════════════════════════════════════════════╝
    `;
    
    console.log(banner);
  }

  private logInitializationErrors(result: any): void {
    if (result && result.criticalErrors && result.criticalErrors.length > 0) {
      logger.error('Critical Errors:');
      result.criticalErrors.forEach((error: string) => logger.error(`  - ${error}`));
    }

    if (result && result.errors && result.errors.length > 0) {
      logger.error('Errors:');
      result.errors.forEach((error: string) => logger.error(`  - ${error}`));
    }

    if (result && result.warnings && result.warnings.length > 0) {
      logger.warn('Warnings:');
      result.warnings.forEach((warning: string) => logger.warn(`  - ${warning}`));
    }

    if (!result) {
      logger.error('No initialization result available');
    }
  }

  private logInitializationSuccess(result: any): void {
    logger.info('📊 Initialization Summary:');
    logger.info(`  ✅ Services Started: ${result.servicesStarted.length}`);
    logger.info(`  ❌ Services Failed: ${result.servicesFailed.length}`);
    logger.info(`  ⚠️ Warnings: ${result.warnings.length}`);
    logger.info(`  ⏱️ Total Time: ${result.startupTime}ms`);

    if (result.servicesStarted.length > 0) {
      logger.info('  Started Services:');
      result.servicesStarted.forEach((service: string) => logger.info(`    - ${service}`));
    }

    if (result.servicesFailed.length > 0) {
      logger.warn('  Failed Services:');
      result.servicesFailed.forEach((service: string) => logger.warn(`    - ${service}`));
    }
  }

  private logSystemReady(): void {
    const status = this.systemInitializer.getSystemStatus();
    
    logger.info('🎯 System Status:');
    logger.info(`  Status: ${status.status.toUpperCase()}`);
    logger.info(`  Uptime: ${Math.round(status.uptime / 1000)}s`);
    logger.info(`  Services: ${Object.values(status.services).filter(Boolean).length}/${Object.keys(status.services).length} healthy`);
    
    const port = process.env.PORT || 3001;
    logger.info(`  Dashboard: http://localhost:${port}`);
    logger.info(`  API: http://localhost:${port}/api`);
    logger.info(`  Health: http://localhost:${port}/health`);

    logger.info('');
    logger.info('🚀 MEV Arbitrage Bot is now LIVE and ready for trading!');
    logger.info('📊 Monitor the dashboard for real-time performance metrics');
    logger.info('🧠 ML learning system is actively adapting strategies');
    logger.info('⚡ Multi-chain opportunity detection is running');
    logger.info('');
  }

  private startPeriodicReporting(): void {
    // Report system status every 5 minutes
    setInterval(() => {
      if (!this.isShuttingDown) {
        this.reportSystemStatus();
      }
    }, 5 * 60 * 1000);

    // Report detailed metrics every hour
    setInterval(() => {
      if (!this.isShuttingDown) {
        this.reportDetailedMetrics();
      }
    }, 60 * 60 * 1000);
  }

  private reportSystemStatus(): void {
    const status = this.systemInitializer.getSystemStatus();
    const healthMonitor = this.systemInitializer.getHealthMonitor();
    
    const healthyServices = Object.values(status.services).filter(Boolean).length;
    const totalServices = Object.keys(status.services).length;
    const uptimeHours = Math.round(status.uptime / (1000 * 60 * 60) * 10) / 10;
    
    logger.info(`📊 System Status Report - Uptime: ${uptimeHours}h, Services: ${healthyServices}/${totalServices}, Status: ${status.status}`);
    
    // Report any recent alerts
    const recentAlerts = healthMonitor.getAlerts('error', 1); // Last hour
    if (recentAlerts.length > 0) {
      logger.warn(`⚠️ Recent alerts: ${recentAlerts.length} in the last hour`);
    }
  }

  private async reportDetailedMetrics(): Promise<void> {
    try {
      const healthMonitor = this.systemInitializer.getHealthMonitor();
      const metrics = await healthMonitor.getHealthMetrics();
      
      if (metrics) {
        const memoryMB = Math.round(metrics.memoryUsage.heapUsed / 1024 / 1024);
        const uptimeHours = Math.round(metrics.uptime / (1000 * 60 * 60) * 10) / 10;
        
        logger.info('📈 Detailed Metrics Report:');
        logger.info(`  Memory Usage: ${memoryMB}MB`);
        logger.info(`  CPU Usage: ${metrics.cpuUsage.toFixed(1)}%`);
        logger.info(`  Uptime: ${uptimeHours}h`);
        logger.info(`  Response Time: ${metrics.performance.responseTime.toFixed(0)}ms`);
        logger.info(`  Throughput: ${metrics.performance.throughput.toFixed(2)} req/s`);
        logger.info(`  Error Rate: ${metrics.performance.errorRate.toFixed(2)}%`);
      }
    } catch (error) {
      logger.error('Error generating detailed metrics report:', error);
    }
  }
}

// Start the application
async function main() {
  const startup = new MEVArbitrageBotStartup();
  await startup.start();
}

// Handle unhandled errors
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start the application
main().catch((error) => {
  logger.error('Failed to start application:', error);
  process.exit(1);
});
