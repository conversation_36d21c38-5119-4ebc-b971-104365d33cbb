import { Router } from 'express';
import logger from '../utils/logger.js';
import { OpportunityDetectionService, ArbitrageType } from '../services/OpportunityDetectionService.js';

export default function createOpportunityRoutes(opportunityService: OpportunityDetectionService) {
  const router = Router();

  // Get all opportunities
  router.get('/', async (req, res) => {
    try {
      const { 
        type, 
        minProfit, 
        maxSlippage, 
        network, 
        asset,
        limit = '50',
        sortBy = 'potentialProfit',
        order = 'desc'
      } = req.query;

      let opportunities = opportunityService.getOpportunities();

      // Apply filters
      if (type && Object.values(ArbitrageType).includes(type as ArbitrageType)) {
        opportunities = opportunities.filter(opp => opp.type === type);
      }

      if (minProfit) {
        const minProfitNum = parseFloat(minProfit as string);
        opportunities = opportunities.filter(opp => opp.potentialProfit >= minProfitNum);
      }

      if (maxSlippage) {
        const maxSlippageNum = parseFloat(maxSlippage as string);
        opportunities = opportunities.filter(opp => opp.slippage <= maxSlippageNum);
      }

      if (network) {
        opportunities = opportunities.filter(opp => opp.network === network);
      }

      if (asset) {
        const assetStr = (asset as string).toUpperCase();
        opportunities = opportunities.filter(opp => 
          opp.assets.some(a => a.toUpperCase().includes(assetStr))
        );
      }

      // Sort opportunities
      const validSortFields = ['potentialProfit', 'profitPercentage', 'timestamp', 'confidence'];
      const sortField = validSortFields.includes(sortBy as string) ? sortBy as string : 'potentialProfit';
      const sortOrder = order === 'asc' ? 1 : -1;

      opportunities.sort((a, b) => {
        const aVal = (a as any)[sortField];
        const bVal = (b as any)[sortField];
        return (aVal - bVal) * sortOrder;
      });

      // Apply limit
      const limitNum = Math.min(parseInt(limit as string) || 50, 100);
      opportunities = opportunities.slice(0, limitNum);

      res.json({
        success: true,
        data: opportunities,
        count: opportunities.length,
        filters: { type, minProfit, maxSlippage, network, asset },
        pagination: { limit: limitNum, sortBy: sortField, order }
      });
    } catch (error) {
      logger.error('Error getting opportunities:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve opportunities'
      });
    }
  });

  // Get specific opportunity
  router.get('/:id', async (req, res) => {
    try {
      const { id } = req.params;
      
      const opportunity = opportunityService.getOpportunity(id);
      
      if (!opportunity) {
        return res.status(404).json({
          success: false,
          error: 'Opportunity not found'
        });
      }

      res.json({
        success: true,
        data: opportunity
      });
    } catch (error) {
      logger.error('Error getting opportunity:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve opportunity'
      });
    }
  });

  // Get opportunities by type
  router.get('/type/:type', async (req, res) => {
    try {
      const { type } = req.params;
      
      if (!Object.values(ArbitrageType).includes(type as ArbitrageType)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid arbitrage type'
        });
      }

      const opportunities = opportunityService.getOpportunities()
        .filter(opp => opp.type === type);

      res.json({
        success: true,
        data: opportunities,
        count: opportunities.length,
        type
      });
    } catch (error) {
      logger.error('Error getting opportunities by type:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve opportunities by type'
      });
    }
  });

  // Get opportunities by asset
  router.get('/asset/:asset', async (req, res) => {
    try {
      const { asset } = req.params;
      const assetUpper = asset.toUpperCase();
      
      const opportunities = opportunityService.getOpportunities()
        .filter(opp => opp.assets.some(a => a.toUpperCase() === assetUpper));

      res.json({
        success: true,
        data: opportunities,
        count: opportunities.length,
        asset: assetUpper
      });
    } catch (error) {
      logger.error('Error getting opportunities by asset:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve opportunities by asset'
      });
    }
  });

  // Get opportunities by network
  router.get('/network/:network', async (req, res) => {
    try {
      const { network } = req.params;
      
      const opportunities = opportunityService.getOpportunities()
        .filter(opp => opp.network === network);

      res.json({
        success: true,
        data: opportunities,
        count: opportunities.length,
        network
      });
    } catch (error) {
      logger.error('Error getting opportunities by network:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve opportunities by network'
      });
    }
  });

  // Get opportunity statistics
  router.get('/stats/summary', async (req, res) => {
    try {
      const stats = opportunityService.getStats();
      const opportunities = opportunityService.getOpportunities();

      // Calculate additional statistics
      const typeDistribution: Record<string, number> = {};
      const networkDistribution: Record<string, number> = {};
      const profitRanges = {
        low: 0,    // < $50
        medium: 0, // $50 - $200
        high: 0    // > $200
      };

      opportunities.forEach(opp => {
        // Type distribution
        typeDistribution[opp.type] = (typeDistribution[opp.type] || 0) + 1;
        
        // Network distribution
        networkDistribution[opp.network] = (networkDistribution[opp.network] || 0) + 1;
        
        // Profit ranges
        if (opp.potentialProfit < 50) {
          profitRanges.low++;
        } else if (opp.potentialProfit <= 200) {
          profitRanges.medium++;
        } else {
          profitRanges.high++;
        }
      });

      const enhancedStats = {
        ...stats,
        typeDistribution,
        networkDistribution,
        profitRanges,
        totalPotentialProfit: opportunities.reduce((sum, opp) => sum + opp.potentialProfit, 0),
        avgConfidence: opportunities.length > 0 
          ? opportunities.reduce((sum, opp) => sum + opp.confidence, 0) / opportunities.length 
          : 0
      };

      res.json({
        success: true,
        data: enhancedStats
      });
    } catch (error) {
      logger.error('Error getting opportunity stats:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve opportunity statistics'
      });
    }
  });

  // Get top opportunities
  router.get('/top/profitable', async (req, res) => {
    try {
      const { limit = '10' } = req.query;
      const limitNum = Math.min(parseInt(limit as string) || 10, 50);
      
      const opportunities = opportunityService.getOpportunities()
        .sort((a, b) => b.potentialProfit - a.potentialProfit)
        .slice(0, limitNum);

      res.json({
        success: true,
        data: opportunities,
        count: opportunities.length,
        limit: limitNum
      });
    } catch (error) {
      logger.error('Error getting top opportunities:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve top opportunities'
      });
    }
  });

  // Get opportunities with high confidence
  router.get('/high-confidence', async (req, res) => {
    try {
      const { minConfidence = '80' } = req.query;
      const minConf = parseFloat(minConfidence as string);
      
      const opportunities = opportunityService.getOpportunities()
        .filter(opp => opp.confidence >= minConf)
        .sort((a, b) => b.confidence - a.confidence);

      res.json({
        success: true,
        data: opportunities,
        count: opportunities.length,
        minConfidence: minConf
      });
    } catch (error) {
      logger.error('Error getting high confidence opportunities:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve high confidence opportunities'
      });
    }
  });

  // Get recent opportunities
  router.get('/recent/:minutes', async (req, res) => {
    try {
      const { minutes } = req.params;
      const minutesNum = parseInt(minutes);
      
      if (isNaN(minutesNum) || minutesNum <= 0) {
        return res.status(400).json({
          success: false,
          error: 'Invalid minutes parameter'
        });
      }

      const cutoff = Date.now() - minutesNum * 60 * 1000;
      const opportunities = opportunityService.getOpportunities()
        .filter(opp => opp.timestamp >= cutoff)
        .sort((a, b) => b.timestamp - a.timestamp);

      res.json({
        success: true,
        data: opportunities,
        count: opportunities.length,
        timeframe: `${minutesNum} minutes`
      });
    } catch (error) {
      logger.error('Error getting recent opportunities:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve recent opportunities'
      });
    }
  });

  return router;
}
